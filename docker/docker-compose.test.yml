version: '3.8'

services:
  robot-studio:
    # 如果使用 Dockerfile 构建
    build:
      context: ..
      dockerfile: Dockerfile

    # 或者使用已有镜像
    # image: your-image-name:tag

    # 容器名称
    container_name: robot-studio-container

    # 挂载卷
    volumes:
      - /root/workspace/logs:/home/<USER>/mindshake/logs
      - /root/workspace/graph_data:/home/<USER>/mindshake/graph_data

    # 环境变量
    environment:
      - ENV_TAG=TEST
      # <DEV代表本地开发环境、TEST代表测试环境(和生产共用配置表)、PROD代表生产环境>
      - LOG_LEVEL=INFO
      # 日志级别
      - DB_URL_PROD=rm-bp1069j22f20cfu32.mysql.rds.aliyuncs.com
      # 生产数据库地址, 仅限内网访问
      - DB_URL_DEV=rm-bp1r8a1y8w872i170oo.mysql.rds.aliyuncs.com
      # 开发数据库地址, 内外网均可访问
      - DB_USER_FILE=/run/secrets/db_user
      - DB_PASSWORD_FILE=/run/secrets/db_password

    secrets:
      - db_user
      - db_password

    # 端口映射
    ports:
      - "8010:8000"

    # 自动重启
    restart: unless-stopped