#!/bin/bash

# Docker镜像构建脚本
# chmod +x docker-build.sh
# ./docker-build.sh <version>
# 示例 ./docker-build.sh 20250804V2


# 检查参数
if [ $# -eq 0 ]; then
    echo "错误: 请提供版本号参数"
    echo "用法: $0 <version>"
    echo "示例: $0 20250804V2"
    exit 1
fi

# 获取版本参数
VERSION=$1

# 定义变量
REGISTRY="crpi-jtf9pgnkrum3djgf.cn-hangzhou.personal.cr.aliyuncs.com"
NAMESPACE="mindshake_ai"
IMAGE_NAME="mindshake_images"
DOCKERFILE="Dockerfile"
BUILD_CONTEXT=".."
PLATFORM="linux/amd64"

# 构建完整的镜像标签
FULL_IMAGE_TAG="${REGISTRY}/${NAMESPACE}/${IMAGE_NAME}:${VERSION}"

echo "开始构建Docker镜像..."
echo "镜像标签: ${FULL_IMAGE_TAG}"
echo "平台: ${PLATFORM}"
echo "Dockerfile: ${DOCKERFILE}"
echo "构建上下文: ${BUILD_CONTEXT}"
echo "----------------------------------------"

# 执行构建命令
docker buildx build \
    --platform ${PLATFORM} \
    -f ${DOCKERFILE} \
    -t ${FULL_IMAGE_TAG} \
    ${BUILD_CONTEXT}

# 检查构建结果
if [ $? -eq 0 ]; then
    echo "----------------------------------------"
    echo "✅ Docker镜像构建成功!"
    echo "镜像标签: ${FULL_IMAGE_TAG}"
    echo ""
    echo "可选操作:"
    echo "1. 推送到仓库: docker push ${FULL_IMAGE_TAG}"
    echo "2. 查看镜像: docker images | grep ${IMAGE_NAME}"
else
    echo "----------------------------------------"
    echo "❌ Docker镜像构建失败!"
    exit 1
fi