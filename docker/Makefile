# Makefile for Docker Compose operations

# 各环境的 compose 文件
COMPOSE_FILE ?= docker-compose.yml
COMPOSE_FILE_DEV ?= docker-compose.dev.yml
COMPOSE_FILE_TEST ?= docker-compose.test.yml
COMPOSE_FILE_PROD ?= docker-compose.prod.yml

# 默认的服务名称
SERVICE_NAME ?= robot-studio
# 项目名称（用于容器前缀）
PROJECT_NAME ?= $(shell basename $(CURDIR))
DOCKERFILE ?= Dockerfile
BUILD_CONTEXT ?= ..
# Docker 构建脚本
DOCKER_BUILD_SCRIPT ?= ./docker-build.sh
# 默认版本 (YYYYMMDDHH格式，精确到小时)
DEFAULT_VERSION := $(shell date +%Y%m%d%H)
REGISTRY ?= crpi-jtf9pgnkrum3djgf.cn-hangzhou.personal.cr.aliyuncs.com/mindshake_ai
IMAGE_NAME ?= mindshake_images
LOCAL_BUILD_IMAGE_NAME ?= mindshake_local_build_image
USER_NAME ?= 用户名
USER_PASSWORD ?= 123456
# 支持 version= 参数
ifdef version
    VERSION := $(version)
else
    VERSION ?= $(DEFAULT_VERSION)
endif
# 完整镜像名称
FULL_IMAGE_NAME := $(REGISTRY)/$(IMAGE_NAME):$(VERSION)

# 帮助信息
.PHONY: help
help:
	@echo ""
	@echo "Docker 管理命令:"
	@echo "  make docker-images              - 查看所有Docker镜像"
	@echo "  make docker-ps                  - 查看所有容器（包括停止的）"
	@echo "  make docker-stop name=容器名    - 停止指定容器"
	@echo "  make docker-rm name=容器名      - 删除指定容器"
	@echo ""
	@echo "Docker 运行命令 (按环境):"
	@echo "  开发环境:"
	@echo "    make up-dev        - 启动开发环境服务（前台运行，显示日志）"
	@echo "    make down-dev      - 停止开发环境服务"
	@echo "    make restart-dev   - 重启开发环境服务"
	@echo "    make logs-dev      - 查看开发环境日志"
	@echo "    make shell-dev     - 进入开发环境容器"
	@echo "    make ps-dev        - 查看开发环境状态"
	@echo ""
	@echo "  测试环境:"
	@echo "    make up-test       - 启动测试环境服务（后台运行）"
	@echo "    make down-test     - 停止测试环境服务"
	@echo "    make restart-test  - 重启测试环境服务"
	@echo "    make logs-test     - 查看测试环境日志"
	@echo "    make shell-test    - 进入测试环境容器"
	@echo "    make ps-test       - 查看测试环境状态"
	@echo ""
	@echo "  生产环境:"
	@echo "    make up-prod       - 启动生产环境服务（后台运行）"
	@echo "    make down-prod     - 停止生产环境服务"
	@echo "    make restart-prod  - 重启生产环境服务"
	@echo "    make logs-prod     - 查看生产环境日志"
	@echo "    make shell-prod    - 进入生产环境容器"
	@echo "    make ps-prod       - 查看生产环境状态"
	@echo ""
	@echo "  默认环境:"
	@echo "    make up            - 启动默认环境服务（后台运行）"
	@echo "    make down          - 停止默认环境服务"
	@echo "    make restart       - 重启默认环境服务"
	@echo "    make logs          - 查看默认环境日志"
	@echo "    make shell         - 进入默认环境容器"
	@echo "    make ps            - 查看默认环境状态"
	@echo ""
	@echo "Docker 镜像拉取:"
	@echo "  make docker-pull                    - 拉取镜像（默认版本，集成登录+拉取）"
	@echo "  make docker-pull version=VER       - 拉取指定版本镜像（集成登录+拉取）"
	@echo ""
	@echo "Docker 镜像构建命令:"
	@echo "  make docker-build                   - 构建本地Docker镜像（默认版本）"
	@echo "  make docker-build version=VER       - 构建指定版本的本地Docker镜像"
	@echo "  make docker-build-arm               - 构建ARM架构本地镜像（默认版本）"
	@echo "  make docker-build-arm version=VER   - 构建指定版本的ARM架构本地镜像"
	@echo "  make docker-buildx                  - 构建多架构镜像（默认版本）"
	@echo "  make docker-buildx version=VER      - 构建指定版本的多架构镜像"
	@echo ""
	@echo "Docker 镜像上传命令:"
	@echo "  make docker-push                    - 上传本地镜像（默认版本，集成登录+标记+推送）"
	@echo "  make docker-push version=VER       - 上传指定版本本地镜像（集成登录+标记+推送）"
	@echo ""
	@echo "Docker 一键部署命令:"
	@echo "  make docker-release                 - 一键发布：构建、上传（默认版本）"
	@echo "  make docker-release version=VER    - 一键发布：构建、上传（指定版本）"

# ===== Docker 管理命令 =====
.PHONY: docker-images docker-ps docker-stop docker-rm

# 查看所有Docker镜像
docker-images:
	docker images

# 查看运行中的容器
docker-ps:
	docker ps -a

# 停止指定容器
docker-stop:
	@if [ -z "$(name)" ]; then \
		echo "错误: 请指定容器名称 name=容器名"; \
		exit 1; \
	fi
	@echo "🛛 停止容器: $(name)"
	docker stop $(name)
	@echo "✅ 容器已停止"

# 删除指定容器
docker-rm:
	@if [ -z "$(name)" ]; then \
		echo "错误: 请指定容器名称 name=容器名"; \
		exit 1; \
	fi
	@echo "🗑️ 删除容器: $(name)"
	docker rm $(name)
	@echo "✅ 容器已删除"

# ===== 开发环境命令 =====
.PHONY: up-dev down-dev restart-dev logs-dev shell-dev ps-dev

up-dev:
	docker-compose -f $(COMPOSE_FILE_DEV) up

down-dev:
	docker-compose -f $(COMPOSE_FILE_DEV) down

restart-dev:
	docker-compose -f $(COMPOSE_FILE_DEV) restart

logs-dev:
	docker-compose -f $(COMPOSE_FILE_DEV) logs -f --tail=100

shell-dev:
	docker-compose -f $(COMPOSE_FILE_DEV) exec $(SERVICE_NAME) bash

ps-dev:
	docker-compose -f $(COMPOSE_FILE_DEV) ps

# ===== 测试环境命令 =====
.PHONY: up-test down-test restart-test logs-test shell-test ps-test

up-test:
	@if [ -f $(COMPOSE_FILE_TEST) ]; then \
		docker-compose -f $(COMPOSE_FILE) -f $(COMPOSE_FILE_TEST) up -d; \
	else \
		echo "未找到 $(COMPOSE_FILE_TEST) 文件"; \
		exit 1; \
	fi

down-test:
	@if [ -f $(COMPOSE_FILE_TEST) ]; then \
		docker-compose -f $(COMPOSE_FILE) -f $(COMPOSE_FILE_TEST) down; \
	else \
		echo "未找到 $(COMPOSE_FILE_TEST) 文件"; \
		exit 1; \
	fi

restart-test:
	@if [ -f $(COMPOSE_FILE_TEST) ]; then \
		docker-compose -f $(COMPOSE_FILE) -f $(COMPOSE_FILE_TEST) restart; \
	else \
		echo "未找到 $(COMPOSE_FILE_TEST) 文件"; \
		exit 1; \
	fi

logs-test:
	@if [ -f $(COMPOSE_FILE_TEST) ]; then \
		docker-compose -f $(COMPOSE_FILE) -f $(COMPOSE_FILE_TEST) logs -f --tail=100; \
	else \
		echo "未找到 $(COMPOSE_FILE_TEST) 文件"; \
		exit 1; \
	fi

shell-test:
	@if [ -f $(COMPOSE_FILE_TEST) ]; then \
		docker-compose -f $(COMPOSE_FILE) -f $(COMPOSE_FILE_TEST) exec $(SERVICE_NAME) bash; \
	else \
		echo "未找到 $(COMPOSE_FILE_TEST) 文件"; \
		exit 1; \
	fi

ps-test:
	@if [ -f $(COMPOSE_FILE_TEST) ]; then \
		docker-compose -f $(COMPOSE_FILE) -f $(COMPOSE_FILE_TEST) ps; \
	else \
		echo "未找到 $(COMPOSE_FILE_TEST) 文件"; \
		exit 1; \
	fi

# ===== 生产环境命令 =====
.PHONY: up-prod down-prod restart-prod logs-prod shell-prod ps-prod

up-prod:
	@if [ -f $(COMPOSE_FILE_PROD) ]; then \
		docker-compose -f $(COMPOSE_FILE) -f $(COMPOSE_FILE_PROD) up -d; \
	else \
		echo "未找到 $(COMPOSE_FILE_PROD) 文件"; \
		exit 1; \
	fi

down-prod:
	@if [ -f $(COMPOSE_FILE_PROD) ]; then \
		docker-compose -f $(COMPOSE_FILE) -f $(COMPOSE_FILE_PROD) down; \
	else \
		echo "未找到 $(COMPOSE_FILE_PROD) 文件"; \
		exit 1; \
	fi

restart-prod:
	@if [ -f $(COMPOSE_FILE_PROD) ]; then \
		docker-compose -f $(COMPOSE_FILE) -f $(COMPOSE_FILE_PROD) restart; \
	else \
		echo "未找到 $(COMPOSE_FILE_PROD) 文件"; \
		exit 1; \
	fi

logs-prod:
	@if [ -f $(COMPOSE_FILE_PROD) ]; then \
		docker-compose -f $(COMPOSE_FILE) -f $(COMPOSE_FILE_PROD) logs -f --tail=100; \
	else \
		echo "未找到 $(COMPOSE_FILE_PROD) 文件"; \
		exit 1; \
	fi

shell-prod:
	@if [ -f $(COMPOSE_FILE_PROD) ]; then \
		docker-compose -f $(COMPOSE_FILE) -f $(COMPOSE_FILE_PROD) exec $(SERVICE_NAME) bash; \
	else \
		echo "未找到 $(COMPOSE_FILE_PROD) 文件"; \
		exit 1; \
	fi

ps-prod:
	@if [ -f $(COMPOSE_FILE_PROD) ]; then \
		docker-compose -f $(COMPOSE_FILE) -f $(COMPOSE_FILE_PROD) ps; \
	else \
		echo "未找到 $(COMPOSE_FILE_PROD) 文件"; \
		exit 1; \
	fi

# ===== 默认环境命令 =====
.PHONY: up down restart logs shell ps

up:
	docker-compose -f $(COMPOSE_FILE) up -d

down:
	docker-compose -f $(COMPOSE_FILE) down

restart:
	docker-compose -f $(COMPOSE_FILE) restart

logs:
	docker-compose -f $(COMPOSE_FILE) logs -f --tail=100

shell:
	docker-compose -f $(COMPOSE_FILE) exec $(SERVICE_NAME) bash

ps:
	docker-compose -f $(COMPOSE_FILE) ps

# ===== Docker 镜像拉取命令 =====
.PHONY: docker-login docker-pull

# 登陆镜像仓库
docker-login:
	@echo "登陆Docker镜像仓库..."
	@echo $(USER_PASSWORD) | docker login -u $(USER_NAME) --password-stdin $(REGISTRY)
	@echo "✅ 登陆成功"

# 集成拉取命令：登陆 + 拉取镜像（默认版本或指定版本）
docker-pull:
	@PULL_VERSION="$(VERSION)"; \
	if [ ! -z "$(version)" ]; then \
		PULL_VERSION="$(version)"; \
	fi; \
	echo "🔄 开始拉取镜像 $(REGISTRY)/$(IMAGE_NAME):$$PULL_VERSION..."; \
	echo "登陆Docker镜像仓库..."; \
	echo $(USER_PASSWORD) | docker login -u $(USER_NAME) --password-stdin $(REGISTRY); \
	echo "🔄 拉取镜像..."; \
	docker pull $(REGISTRY)/$(IMAGE_NAME):$$PULL_VERSION; \
	echo "✅ 镜像拉取完成: $(REGISTRY)/$(IMAGE_NAME):$$PULL_VERSION"

# ===== Docker 镜像构建命令 =====
.PHONY: docker-build docker-build-arm docker-buildx

# 构建本地 Docker 镜像 (x86架构)
docker-build:
	@echo "🔨 构建本地Docker镜像 (版本: $(VERSION), 名称: $(LOCAL_BUILD_IMAGE_NAME))..."
	docker build \
		--build-arg VERSION=$(VERSION) \
		-f $(DOCKERFILE) \
		-t $(LOCAL_BUILD_IMAGE_NAME):$(VERSION) \
		$(BUILD_CONTEXT)
	@echo "✅ 本地镜像构建完成: $(LOCAL_BUILD_IMAGE_NAME):$(VERSION)"

# 构建本地 Docker 镜像 (ARM架构)
docker-build-arm:
	@echo "🔨 构建本地ARM架构Docker镜像 (版本: $(VERSION), 名称: $(LOCAL_BUILD_IMAGE_NAME))..."
	docker build \
		--platform linux/arm64 \
		--build-arg VERSION=$(VERSION) \
		-f $(DOCKERFILE) \
		-t $(LOCAL_BUILD_IMAGE_NAME):$(VERSION) \
		$(BUILD_CONTEXT)
	@echo "✅ 本地ARM镜像构建完成: $(LOCAL_BUILD_IMAGE_NAME):$(VERSION)"

# 构建多架构镜像（需要 Docker Buildx）
docker-buildx:
	@echo "🔨 构建多架构镜像 (版本: $(VERSION), 名称: $(LOCAL_BUILD_IMAGE_NAME))..."
	docker buildx build \
		--platform linux/amd64,linux/arm64 \
		--build-arg VERSION=$(VERSION) \
		-f $(DOCKERFILE) \
		-t $(LOCAL_BUILD_IMAGE_NAME):$(VERSION) \
		$(BUILD_CONTEXT)
	@echo "✅ 多架构镜像构建完成: $(LOCAL_BUILD_IMAGE_NAME):$(VERSION)"

# ===== Docker 镜像上传命令 =====
.PHONY: docker-push

# 集成上传命令：登录 + 标记 + 推送本地镜像（默认版本或指定版本）
docker-push:
	@PUSH_VERSION="$(VERSION)"; \
	if [ ! -z "$(version)" ]; then \
		PUSH_VERSION="$(version)"; \
	fi; \
	echo "🚀 开始上传本地镜像 $(LOCAL_BUILD_IMAGE_NAME):$$PUSH_VERSION..."; \
	echo "登陆Docker镜像仓库..."; \
	echo $(USER_PASSWORD) | docker login -u $(USER_NAME) --password-stdin $(REGISTRY); \
	echo "🏷️  标记镜像..."; \
	docker tag $(LOCAL_BUILD_IMAGE_NAME):$$PUSH_VERSION $(REGISTRY)/$(IMAGE_NAME):$$PUSH_VERSION; \
	echo "📤 推送镜像..."; \
	docker push $(REGISTRY)/$(IMAGE_NAME):$$PUSH_VERSION; \
	echo "✅ 镜像上传完成: $(REGISTRY)/$(IMAGE_NAME):$$PUSH_VERSION"

# ===== Docker 一键部署命令 =====
.PHONY: docker-release

# 一键发布：构建 + 上传（默认版本或指定版本）
docker-release:
	@RELEASE_VERSION="$(VERSION)"; \
	if [ ! -z "$(version)" ]; then \
		RELEASE_VERSION="$(version)"; \
	fi; \
	echo "🚀 开始发布版本 $$RELEASE_VERSION..."; \
	echo "🔨 构建本地镜像..."; \
	$(MAKE) docker-build version=$$RELEASE_VERSION; \
	echo "📤 上传镜像..."; \
	$(MAKE) docker-push version=$$RELEASE_VERSION; \
	echo "✅ 版本 $$RELEASE_VERSION 发布完成！"