/**
 * 组件配置常量
 * 统一管理前端的component配置信息，避免多处硬编码
 */

// 默认组件配置
export const DEFAULT_COMPONENT_CONFIG = {
  component_id: "cmp_dfe82e",
  version: 1,
  executor_config: {}
} as const;

// 组件配置类型定义
export interface ComponentConfig {
  component_id: string;
  version: number;
  executor_config?: Record<string, any>;
}

// 预定义的组件配置选项
export const COMPONENT_CONFIGS = {
  // 默认聊天助手
  CHAT_ASSISTANT: {
    component_id: "cmp_dfe82e",
    version: 1,
    executor_config: {}
  },

  // 可以在这里添加更多预定义的组件配置
  // KNOWLEDGE_RAG: {
  //   component_id: "cmp_knowledge_rag",
  //   version: 1,
  //   executor_config: {
  //     rag_enabled: true
  //   }
  // }
} as const;

// 组件配置工具函数
export const ComponentConfigUtils = {
  /**
   * 获取默认组件配置
   */
  getDefault(): ComponentConfig {
    return { ...DEFAULT_COMPONENT_CONFIG };
  },

  /**
   * 根据类型获取预定义配置
   */
  getByType(type: keyof typeof COMPONENT_CONFIGS): ComponentConfig {
    return { ...COMPONENT_CONFIGS[type] };
  },

  /**
   * 验证组件配置
   */
  validate(config: Partial<ComponentConfig>): boolean {
    return !!(config.component_id && config.version);
  },

  /**
   * 合并配置（用户配置覆盖默认配置）
   */
  merge(customConfig: Partial<ComponentConfig>): ComponentConfig {
    return {
      ...DEFAULT_COMPONENT_CONFIG,
      ...customConfig
    };
  }
};
