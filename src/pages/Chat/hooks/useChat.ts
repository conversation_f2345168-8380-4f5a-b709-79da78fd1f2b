import { useState, useCallback, useEffect } from 'react';
import { flushSync } from 'react-dom';
import type { MessageInstance } from 'antd/es/message/interface';
import { chainlitPerfMonitor } from '../utils/chainlitPerformanceTest';
import { useWebSocketChat } from './useWebSocketChat';
import {
  createSession,
  getUserSessions,
  getSessionDetail,
  deleteSession,
  getSessionMessages,
  getArtifactList,
  downloadArtifact,
  deleteArtifact,
  SessionInfo,
  SessionDetail,
  ArtifactDetail,
  CreateSessionParams,
} from '@/services/chat';
import { ComponentConfigUtils, type ComponentConfig } from '@/config/componentConfig';

export interface ChatMessage {
  id: string;
  chunk_id?: string;
  type: 'user' | 'ai';
  content: Array<{
    id: string;
    type: 'text' | 'image' | 'code' | 'link' | 'table' | 'markdown';
    content: string;
  }>;
  timestamp: string;
  isThinking?: boolean; // 标识当前消息是否是思考过程
  isStreaming?: boolean; // 标识当前消息是否正在流式输出
  typing?: boolean; // 控制打字机效果
  chunkSubType?: string; // 🔥 NEW: 用于区分不同类型的流式内容
  thinking?: Array<{
    id: string;
    title: string;
    content: string;
    type: 'thinking' | 'toolRequest' | 'toolResult' | 'search' | 'code';
  }>;
  artifacts?: ArtifactDetail[];
}

export interface Product {
  id: string;
  type: 'code' | 'webpage' | 'image';
  content: string;
  timestamp: string;
  title: string;
  description?: string;
}

export const useChat = (messageApi: MessageInstance, customData: any) => {
  const [currentSession, setCurrentSession] = useState<SessionDetail | null>(null);
  const [sessions, setSessions] = useState<SessionInfo[]>([]);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);
  const [streaming, setStreaming] = useState(false);
  const [currentUserId, setCurrentUserId] = useState<string>(''); // 初始为空，等待 customData
  const [currentUserName, setCurrentUserName] = useState<string>('用户');
  const [currentCid, setCurrentCid] = useState<string>('default_company');
  const [useWebSocket, setUseWebSocket] = useState(true); // 🔥 WebSocket开关

  // 监听传入的customData变化
  useEffect(() => {
    if (customData && (customData as any).uid ) {
      setCurrentUserId((customData as any).uid);
    }
    if (customData && (customData as any).username) {
      setCurrentUserName((customData as any).username);
    }
    if (customData && (customData as any).cid) {
      setCurrentCid((customData as any).cid);
    }
  }, [customData]);

  // 🔥 Chainlit WebSocket集成 - 按需连接，不自动连接
  const webSocketChat = useWebSocketChat(messageApi, {
    autoConnect: false, // 改为false，避免进入页面就自动连接
    enablePerformanceMonitoring: true,
    messageAcknowledgment: true,
  });

  // 🔥 Chainlit WebSocket事件监听器
  useEffect(() => {
    const handleMessageUpdate = (event: CustomEvent) => {
      const { messageId, chunk_id, content, chunkSubType, isStreaming, isThinking, updateType } = event.detail;

      console.log(`[CHAINLIT-WS] 📝 Message update event: messageId=${messageId}, chunk_id=${chunk_id}, chunkSubType=${chunkSubType}, updateType=${updateType || 'append'}, content="${content}" (${content?.length || 0} chars), isStreaming=${isStreaming}`);

      flushSync(() => {
        setMessages(prev => {
          const updated = [...prev];
          const messageIndex = updated.findIndex(msg => msg.id === messageId);

          if (messageIndex !== -1) {
            const message = { ...updated[messageIndex] };

            // 🔥 handleMessageUpdate 只更新thinking，不更新content
            // 内容更新由 handleMessageComplete 处理
            console.log(`[CHAINLIT-WS] 📝 Only updating thinking, content will be handled by handleMessageComplete`);

            message.typing = false;
            message.isStreaming = isStreaming;
            message.chunkSubType = chunkSubType;
            
            // 🔥 根据chunkSubType决定是思考过程还是内容流
            if (chunkSubType === 'ModelClientStreamingChunkEvent') {
              // 如果是模型流式输出，设置为非思考状态，内容放在content中
              message.isThinking = false;
              
              // 🔥 如果content为空，不进行任何操作
              if (!content || content.trim() === '') {
                console.log(`[CHAINLIT-WS] ⚠️ Empty content received, skipping content update for message ${messageId}`);
              } else {
                // 确保content数组存在
                if (!message.content) {
                  message.content = [];
                }
                
                // 检查是否已存在相同id的内容块
                const existingChunkIndex = message.content.findIndex(chunk => chunk.id === chunk_id);
                
                if (existingChunkIndex !== -1) {
                  // 如果存在相同id，追加到现有内容块
                  message.content[existingChunkIndex] = {
                    ...message.content[existingChunkIndex],
                    content: message.content[existingChunkIndex].content + content
                  };
                  console.log(`[CHAINLIT-WS] ➕ Content APPENDED: chunk_id=${chunk_id}, existingChunkIndex=${existingChunkIndex}`);
                } else {
                  // 如果不存在相同id，推送新的内容块到数组
                  message.content.push({
                    id: chunk_id || `chunk-${Date.now()}`,
                    type: 'text',
                    content: content
                  });
                  console.log(`[CHAINLIT-WS] ➕ Content PUSHED: new chunk with id=${chunk_id || `chunk-${Date.now()}`}`);
                }
              }
              
              console.log(`[CHAINLIT-WS] 📝 Model streaming content updated for message ${messageId}`);
            } else {
              // 其他情况按原来的思考过程处理
              message.isThinking = isThinking;
              if (isThinking) {
                // 🔥 如果是思考过程，填充thinking内容
                if (!message.thinking) {
                  message.thinking = [];
                }
                
                // 根据chunkSubType判断思考类型
                let thinkingType: 'thinking' | 'toolRequest' | 'toolResult' | 'search' | 'code' = 'thinking';
                if (chunkSubType === 'ToolCallRequestEvent') {
                  thinkingType = 'toolRequest';
                } else if (chunkSubType === 'ToolResultMessage') {
                  thinkingType = 'toolResult';
                } else if (chunkSubType === 'search') {
                  thinkingType = 'search';
                } else if (chunkSubType === 'code') {
                  thinkingType = 'code';
                }
                
                // 创建或更新思考项
                const thinkingId = chunk_id
                console.log(`[CHAINLIT-WS] 🧠 Thinking ID: ${thinkingId}, message: ${JSON.stringify(message)}`);
                const thinkingItem = {
                  id: thinkingId,
                  title: `思考过程 ${thinkingId}`,
                  content: content || '正在思考...',
                  type: thinkingType,
                };
                
                // 如果是新的思考类型，添加到数组；如果是同一类型，更新内容
                const existingThinkingIndex = message.thinking.findIndex(t => t.id === thinkingId);
                if (existingThinkingIndex !== -1) {
                  console.log(`[CHAINLIT-WS] 🧠 Thinking ID: ${thinkingId}, existingThinkingIndex: ${existingThinkingIndex}, message: ${JSON.stringify(message)}`);
                  // 更新现有思考项
                  message.thinking[existingThinkingIndex] = {
                    ...message.thinking[existingThinkingIndex],
                    content: message.thinking[existingThinkingIndex].content + content,
                  };
                } else {
                  // 添加新的思考项
                  message.thinking.push(thinkingItem);
                }
                
                console.log(`[CHAINLIT-WS] 🧠 Thinking updated for message ${messageId}, type: ${thinkingType}, content: "${content}"`);
              }
            }

            updated[messageIndex] = message;
            console.log(`[CHAINLIT-WS] ✅ Message ${messageId} updated successfully, message: ${JSON.stringify(message)}`);
          } else {
            console.warn(`[CHAINLIT-WS] ⚠️ Message ${messageId} not found for update`);
          }
          return updated;
        });
      });
    };

    const handleMessageComplete = (event: CustomEvent) => {
      const { messageId, chunk_id, chunkSubType, completeContent, isThinking, updateType } = event.detail;

      console.log(`[CHAINLIT-WS] 🏁 Message complete event: messageId=${messageId}, chunk_id=${chunk_id}, chunkSubType=${chunkSubType} , updateType=${updateType}, hasCompleteContent=${!!completeContent}, contentLength=${completeContent?.length || 0}`);

      flushSync(() => {
        setMessages(prev => {
          const updated = [...prev];
          const messageIndex = updated.findIndex(msg => msg.id === messageId);

          if (messageIndex !== -1) {
            const message = { ...updated[messageIndex] };
            const oldLength = message.content ? message.content.reduce((total, item) => total + item.content.length, 0) : 0;

                        // 🔥 如果有完整内容，推送到content数组
            if (completeContent && completeContent.trim() !== '') {
              // 确保content数组存在
              if (!message.content) {
                message.content = [];
              }
              
              let type: 'text' | 'image' | 'code' | 'link' | 'table' | 'markdown' = 'text';
              if (chunkSubType === 'TextMessage') {
                type = 'text';
              } else if (chunkSubType === 'OssUrlMessage') {
                type = 'image';
              }

              console.log(`[CHAINLIT-WS] 🔄 Content complete: chunk_id=${chunk_id}, type=${type}`);
              
              // 检查是否已存在相同id的内容块
              const existingChunkIndex = message.content.findIndex(chunk => chunk.id === chunk_id);
              
              if (existingChunkIndex !== -1) {
                // 如果存在相同id，覆盖现有内容块
                message.content[existingChunkIndex] = {
                  ...message.content[existingChunkIndex],
                  type: type,
                  content: completeContent
                };
                console.log(`[CHAINLIT-WS] 🔄 Content OVERWRITTEN: chunk_id=${chunk_id}, existingChunkIndex=${existingChunkIndex}`);
              } else {
                // 如果不存在相同id，推送新的内容块到数组
                message.content.push({
                  id: chunk_id || `chunk-${Date.now()}`,
                  type: type,
                  content: completeContent
                });
                console.log(`[CHAINLIT-WS] ➕ Content PUSHED: new chunk with id=${chunk_id},type=${type}`);
              }
              
              const newLength = message.content.reduce((total, item) => total + item.content.length, 0);
              console.log(`[CHAINLIT-WS] 📊 Content stats: ${oldLength} → ${newLength} chars (${message.content.length} chunks)`);
              console.log(`[CHAINLIT-WS] 📋 Final content preview: "${completeContent.substring(0, 100)}${completeContent.length > 100 ? '...' : ''}"`);

              // 🔥 验证内容是否真的被更新了
              if (newLength !== oldLength) {
                console.log(`[CHAINLIT-WS] ✅ Content successfully updated - length changed`);
              } else {
                console.log(`[CHAINLIT-WS] ✅ Content length unchanged - duplicate content or empty chunk`);
              }
            } else {
              console.log(`[CHAINLIT-WS] ℹ️ Completion event without content or empty content - only marking as complete`);
            }

            message.typing = false;
            message.isStreaming = false;
            message.isThinking = isThinking;
            message.chunkSubType = chunkSubType;
            updated[messageIndex] = message;
            console.log(`[CHAINLIT-WS] ✅ Message ${messageId} marked as complete,message:, ${JSON.stringify(message)}`);
          } else {
            console.warn(`[CHAINLIT-WS] ⚠️ Message ${messageId} not found for completion`);
          }
          return updated;
        });
      });
    };

    const handleArtifactsUpdate = (event: CustomEvent) => {
      const { messageId, artifacts } = event.detail;

      flushSync(() => {
        setMessages(prev => {
          const updated = [...prev];
          const messageIndex = updated.findIndex(msg => msg.id === messageId);

          if (messageIndex !== -1) {
            const message = { ...updated[messageIndex] };
            if (!message.artifacts) message.artifacts = [];

            artifacts.forEach((artifact: any) => {
              const existingArtifact = message.artifacts?.find(a => a.artifact_id === artifact.artifact_id);
              if (!existingArtifact) {
                message.artifacts?.push({
                  id: parseInt(artifact.artifact_id || '0'),
                  artifact_id: artifact.artifact_id,
                  session_id: currentSession?.session_id || '',
                  message_id: messageId,
                  type: artifact.type,
                  title: artifact.title,
                  description: artifact.description || '',
                  content: artifact.content || '',
                  file_path: artifact.file_path || '',
                  file_url: artifact.file_url || '',
                  file_size: artifact.file_size || 0,
                  mime_type: artifact.mime_type || '',
                  file_hash: artifact.file_hash || '',
                  thumbnail_path: artifact.thumbnail_path || '',
                  tags: artifact.tags || '',
                  status: artifact.status || 1,
                  download_count: artifact.download_count || 0,
                  expires_at: artifact.expires_at || '',
                  is_deleted: artifact.is_deleted || false,
                  gmt_create: artifact.gmt_create || new Date().toISOString(),
                  gmt_modified: artifact.gmt_modified || new Date().toISOString(),
                  artifact_metadata: artifact.artifact_metadata || '',
                });
              }
            });
            updated[messageIndex] = message;
          }
          return updated;
        });
      });
    };

    const handleStreamFinish = (event: CustomEvent) => {
      const { messageId, finishReason, usageInfo } = event.detail;

      console.log(`[CHAINLIT-WS] 🏁 Stream finished event received: ${finishReason}`, usageInfo);

      // 🔥 只处理全局流式状态，避免与handleMessageComplete重复处理消息状态
      setStreaming(false);
      console.log(`[CHAINLIT-WS] 🏁 Main streaming state set to false`);

      // 🔥 可选：记录使用信息，但不修改消息状态（已由handleMessageComplete处理）
      if (usageInfo) {
        console.log(`[CHAINLIT-WS] 📊 Usage info:`, usageInfo);
      }
    };

    // 注册事件监听器
    window.addEventListener('chainlit-message-update', handleMessageUpdate as EventListener);
    window.addEventListener('chainlit-message-complete', handleMessageComplete as EventListener);
    window.addEventListener('chainlit-artifacts-update', handleArtifactsUpdate as EventListener);
    window.addEventListener('chainlit-stream-finish', handleStreamFinish as EventListener);

    return () => {
      // 清理事件监听器
      window.removeEventListener('chainlit-message-update', handleMessageUpdate as EventListener);
      window.removeEventListener('chainlit-message-complete', handleMessageComplete as EventListener);
      window.removeEventListener('chainlit-artifacts-update', handleArtifactsUpdate as EventListener);
      window.removeEventListener('chainlit-stream-finish', handleStreamFinish as EventListener);
    };
  }, [currentSession]);

  // 测试流式输出的模拟函数
  const testStreaming = useCallback(async () => {
    const testMessage = "这是一个测试流式输出的消息。我们将模拟逐字符显示的效果，就像ChatGPT一样。这个功能可以让用户看到AI正在实时生成回复，提供更好的用户体验。";

    // 添加用户消息
    const userMessage: ChatMessage = {
      id: `user-${Date.now()}`,
      type: 'user',
      content: [{
        id: `user-${Date.now()}`,
        type: 'text',
        content: '测试流式输出'
      }],
      timestamp: new Date().toISOString(),
    };

    setMessages(prev => [...prev, userMessage]);

    // 创建AI消息占位符
    const aiMessageId = `ai-${Date.now()}`;
    const aiMessage: ChatMessage = {
      id: aiMessageId,
      type: 'ai',
      content: [{
        id: aiMessageId,
        type: 'text',
        content: ''
      }],
      timestamp: new Date().toISOString(),
      isStreaming: true,
      typing: true,
      thinking: [],
    };

    setMessages(prev => [...prev, aiMessage]);

    // 模拟逐字符流式输出
    for (let i = 0; i <= testMessage.length; i++) {
      await new Promise(resolve => {setTimeout(resolve, 50)}); // 50ms延迟

      setMessages(prev => {
        const updated = [...prev];
        const aiMessageIndex = updated.findIndex(msg => msg.id === aiMessageId);

        if (aiMessageIndex !== -1) {
          updated[aiMessageIndex] = {
            ...updated[aiMessageIndex],
            content: [{
              id: aiMessageId,
              type: 'text',
              content: testMessage.substring(0, i)
            }],
            isStreaming: i < testMessage.length,
            typing: i < testMessage.length,
          };
        }

        return updated;
      });
    }
  }, []);

  // 创建新会话
  const createNewSession = useCallback(async (title?: string, componentConfig?: ComponentConfig, query?: string): Promise<SessionDetail | null> => {
    try {
      setLoading(true);
      
      // 根据query生成title，如果query存在则截取前30个字符并加上...
      let sessionTitle = title;
      if (!sessionTitle && query) {
        const truncatedQuery = query.length > 30 ? query.substring(0, 30) + '...' : query;
        sessionTitle = truncatedQuery;
      } else if (!sessionTitle) {
        sessionTitle = `对话 - ${new Date().toLocaleString()}`;
      }
      
      const params: CreateSessionParams = {
        user_id: currentUserId,
        title: sessionTitle,
        component_config: componentConfig || ComponentConfigUtils.getDefault()
      };

      const response = await createSession(params);
      if (response.success && response.data) {
        const sessionDetail = await getSessionDetail(response.data.session_id);
        if (sessionDetail.success && sessionDetail.data) {
          setCurrentSession(sessionDetail.data);
          setMessages([]);
          setProducts([]);
          return sessionDetail.data;
        }
      } else {
        messageApi.error(response.error || '创建会话失败');
      }
    } catch (error) {
      console.error('创建会话失败:', error);
      messageApi.error('创建会话失败');
    } finally {
      setLoading(false);
    }
    return null;
  }, [currentUserId, messageApi]);

  // 获取用户会话列表
  const loadUserSessions = useCallback(async () => {
    // 如果没有有效的用户ID，不执行查询
    if (!currentUserId) {
      console.log('[CHAT] ⏳ Waiting for valid user ID before loading sessions');
      return;
    }

    try {
      setLoading(true);
      console.log(`[CHAT] 🔍 Loading sessions for user: ${currentUserId}`);
      const response = await getUserSessions(currentUserId);
      if (response.success && response.data) {
        setSessions(response.data.sessions);
        console.log(`[CHAT] ✅ Loaded ${response.data.sessions.length} sessions`);
      } else {
        messageApi.error(response.error || '获取会话列表失败');
      }
    } catch (error) {
      console.error('获取会话列表失败:', error);
      messageApi.error('获取会话列表失败');
    } finally {
      setLoading(false);
    }
  }, [currentUserId, messageApi]);

  // 加载会话详情和消息
  const loadSession = useCallback(async (sessionId: string) => {
    try {
      // 移除全局loading设置，避免影响历史列表显示
      // setLoading(true);

      // 获取会话详情
      const sessionResponse = await getSessionDetail(sessionId);
      if (!sessionResponse.success || !sessionResponse.data) {
        messageApi.error('会话不存在');
        return;
      }

      setCurrentSession(sessionResponse.data);

      // 获取会话消息
      const messagesResponse = await getSessionMessages(sessionId);
      if (messagesResponse.success && messagesResponse.data) {
        const chatMessages: ChatMessage[] = messagesResponse.data.messages.map(msg => ({
          id: msg.message_id,
          type: msg.role_type === 'user' ? 'user' : 'ai',
          content: [{
            id: msg.message_id,
            type: 'text',
            content: msg.content
          }],
          timestamp: new Date().toISOString(), // 使用当前时间作为时间戳
          isStreaming: false, // 历史消息不应该显示流式状态
        }));
        setMessages(chatMessages);
      }

      // 获取会话产物
      const artifactsResponse = await getArtifactList({ session_id: sessionId });
      if (artifactsResponse.success && artifactsResponse.data) {
        const productsList: Product[] = artifactsResponse.data.artifacts.map(artifact => ({
          id: artifact.artifact_id,
          type: artifact.type as 'code' | 'webpage' | 'image',
          content: artifact.content,
          timestamp: artifact.gmt_create,
          title: artifact.title,
          description: artifact.description,
        }));
        setProducts(productsList);
      }

    } catch (error) {
      console.error('加载会话失败:', error);
      messageApi.error('加载会话失败');
    }
    // 移除finally中的loading设置
    // finally {
    //   setLoading(false);
    // }
  }, [messageApi]);

  // 🔥 Chainlit架构：发送消息并获取真正的实时流式响应（WebSocket优先）
  const sendMessage = useCallback(async (content: string) => {
    if (!content.trim()) return;

    // 如果没有当前会话，自动创建一个新会话
    let sessionToUse = currentSession;
    if (!sessionToUse) {
      try {
        // 使用默认的组件配置创建会话，并传递用户输入的content作为query
        sessionToUse = await createNewSession(undefined, ComponentConfigUtils.getDefault(), content.trim());
        if (!sessionToUse) {
          messageApi.error('创建会话失败，无法发送消息');
          return;
        }
      } catch (error) {
        messageApi.error('创建会话失败，无法发送消息');
        return;
      }
    }

    // 添加用户消息
    const userMessage: ChatMessage = {
      id: `user-${Date.now()}`,
      type: 'user',
      content: [{
        id: `user-${Date.now()}`,
        type: 'text',
        content: content.trim()
      }],
      timestamp: new Date().toISOString(),
    };

    setMessages(prev => [...prev, userMessage]);
    setStreaming(true);
    console.log('[CHAINLIT] 🚀 Main streaming state set to true');

    // 创建AI消息占位符
    const aiMessageId = `ai-${Date.now()}`;
    const aiMessage: ChatMessage = {
      id: aiMessageId,
      type: 'ai',
      content: [{
        id: aiMessageId,
        type: 'text',
        content: ''
      }],
      timestamp: new Date().toISOString(),
      isStreaming: true, // 标识为正在流式输出
      typing: true, // 启用打字机效果
      thinking: [],
    };

    setMessages(prev => [...prev, aiMessage]);

    try {
      // 🔥 使用WebSocket进行实时通信
      if (useWebSocket) {
        // 如果WebSocket未连接，先连接
        if (!webSocketChat.isConnected) {
          console.log('[CHAINLIT] 🔌 WebSocket not connected, connecting now...');
          await webSocketChat.connectWebSocket();
        }

        console.log('[CHAINLIT] 🚀 Using WebSocket for real-time streaming...');

        await webSocketChat.sendWebSocketMessage(
          sessionToUse.session_id,
          content.trim(),
          currentCid,
          currentUserId,
          currentUserName,
          aiMessageId
        );

        console.log('[CHAINLIT] ✅ WebSocket message sent successfully');

      } else {
        console.error('[CHAINLIT] ❌ WebSocket not connected');
        messageApi.error('WebSocket连接未建立，请刷新页面重试');

        // 移除失败的AI消息
        setMessages(prev => prev.filter(msg => msg.id !== aiMessageId));
        setStreaming(false);
        return;
      }

    } catch (error) {
      console.error('[CHAINLIT] ❌ Send message failed:', error);
      messageApi.error('发送消息失败');

      // 移除失败的AI消息
      setMessages(prev => prev.filter(msg => msg.id !== aiMessageId));
      setStreaming(false);
    }
  }, [currentSession, currentUserId, createNewSession, messageApi, useWebSocket, webSocketChat]);

  // 删除会话
  const deleteSessionById = useCallback(async (sessionId: string) => {
    try {
      const response = await deleteSession(sessionId);
      if (response.success) {
        messageApi.success('会话删除成功');
        setSessions(prev => prev.filter(session => session.session_id !== sessionId));

        // 如果删除的是当前会话，清空当前会话
        if (currentSession?.session_id === sessionId) {
          setCurrentSession(null);
          setMessages([]);
          setProducts([]);
        }
      } else {
        messageApi.error(response.error || '删除会话失败');
      }
    } catch (error) {
      console.error('删除会话失败:', error);
      messageApi.error('删除会话失败');
    }
  }, [currentSession, messageApi]);

  // 下载产物
  const downloadArtifactById = useCallback(async (artifactId: string) => {
    try {
      const response = await downloadArtifact(artifactId);
      const blob = new Blob([response]);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `artifact-${artifactId}.txt`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      messageApi.success('产物下载成功');
    } catch (error) {
      console.error('下载产物失败:', error);
      messageApi.error('下载产物失败');
    }
  }, [messageApi]);

  // 删除产物
  const deleteArtifactById = useCallback(async (artifactId: string) => {
    try {
      const response = await deleteArtifact(artifactId);
      if (response.success) {
        messageApi.success('产物删除成功');
        setProducts(prev => prev.filter(product => product.id !== artifactId));
      } else {
        messageApi.error(response.error || '删除产物失败');
      }
    } catch (error) {
      console.error('删除产物失败:', error);
      messageApi.error('删除产物失败');
    }
  }, [messageApi]);

  return {
    // 状态
    currentSession,
    sessions,
    messages,
    products,
    loading,
    streaming: streaming || (webSocketChat?.streaming || false),
    currentUserId,

    // 🔥 WebSocket状态
    useWebSocket,
    connectionState: webSocketChat?.connectionState || 'disconnected',
    connectionLatency: webSocketChat?.connectionLatency,
    reconnectCount: webSocketChat?.reconnectCount || 0,
    isWebSocketConnected: webSocketChat?.isConnected || false,

    // 方法
    createNewSession,
    loadUserSessions,
    loadSession,
    sendMessage,
    deleteSessionById,
    downloadArtifactById,
    deleteArtifactById,
    testStreaming, // 添加测试函数

    // 🔥 WebSocket方法
    setUseWebSocket,
    connectWebSocket: webSocketChat?.connectWebSocket || (() => Promise.resolve()),
    disconnectWebSocket: webSocketChat?.disconnectWebSocket || (() => {}),
    getPerformanceMetrics: webSocketChat?.getPerformanceMetrics || (() => ({})),
    isChainlitCompliant: webSocketChat?.isChainlitCompliant || (() => false),

    // 🔥 状态重置方法
    setCurrentSession,
    setMessages,
  };
};
