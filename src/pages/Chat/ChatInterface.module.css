/* 对话界面样式 */

.chatContainer {
  height: 80%;
  background: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* 欢迎页面样式 */
.welcomeContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  min-height: 100vh;
}

.welcomeContent {
  max-width: 800px;
  width: 100%;
  text-align: center;
}

.avatarContainer {
  position: relative;
  display: inline-block;
  margin-bottom: 24px;
}

.avatarGlow {
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  background: linear-gradient(45deg, rgba(255,255,255,0.3), rgba(255,255,255,0.1));
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.05); opacity: 0.7; }
  100% { transform: scale(1); opacity: 1; }
}

.inputCard {
  width: 100%;
  max-width: 600px;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.1);
}

.promptGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 16px;
  margin-top: 24px;
}

.promptCard {
  border-radius: 12px;
  background: rgba(255,255,255,0.1);
  border: 1px solid rgba(255,255,255,0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  cursor: pointer;
}

.promptCard:hover {
  background: rgba(255,255,255,0.2);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* 对话模式样式 */
.chatMode {
  flex: 1;
  display: flex;
}

.leftPanel {
  flex: 1;
  background: #ffffff;
  border-right: 1px solid #f0f0f0;
  display: flex;
  flex-direction: column;
}

.rightPanel {
  width: 400px;
  background: #ffffff;
  display: flex;
  flex-direction: column;
}

.panelHeader {
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.messagesContainer {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  background: #f8f9fa;
}

.messageCard {
  margin-bottom: 16px;
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  transition: all 0.2s ease;
  cursor: pointer;
}

.messageCard:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  transform: translateY(-1px);
}

.messageCard.selected {
  border: 2px solid #1890ff;
  box-shadow: 0 4px 12px rgba(24,144,255,0.15);
  transform: scale(1.02);
}

.messageContent {
  font-size: 15px;
  line-height: 1.6;
  margin: 0;
  word-break: break-word;
}

.thinkingContainer {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.thinkingStep {
  margin-bottom: 12px;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  transition: all 0.2s ease;
  cursor: pointer;
}

.thinkingStep:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  transform: translateY(-1px);
}

.thinkingStep.selected {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24,144,255,0.15);
}

.codeBlock {
  background: #f6f8fa;
  padding: 12px;
  border-radius: 6px;
  font-size: 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  margin: 8px 0;
  white-space: pre-wrap;
  word-break: break-word;
  border: 1px solid #e1e4e8;
}

.emptyState {
  text-align: center;
  padding: 60px 20px;
  color: #999;
}

.emptyStateIcon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

/* 侧边栏样式 */
.sidebarOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.45);
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.sidebarPanel {
  position: fixed;
  top: 0;
  left: -360px;
  width: 360px;
  height: 100vh;
  background: #ffffff;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
  z-index: 1001;
  transition: left 0.3s ease;
  display: flex;
  flex-direction: column;
}

.sidebarPanel.sidebarOpen {
  left: 0;
}

.sidebarHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.sidebarContent {
  flex: 1;
  padding: 16px 0;
  overflow-y: auto;
}

.historyItem {
  padding: 12px 16px;
  cursor: pointer;
  border-left: 3px solid transparent;
  transition: all 0.2s ease;
  border-radius: 0 8px 8px 0;
  margin-bottom: 4px;
}

.historyItem:hover {
  background-color: #f0f2ff;
  border-left-color: #1890ff;
  transform: translateX(2px);
}

.historyTitle {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
  color: #262626;
}

.historyPreview {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 4px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.historyTime {
  font-size: 11px;
  color: #bfbfbf;
}

/* Tab内容样式 */
.tabContent {
  padding: 0 16px;
}

/* 产物项目样式 */
.productItem {
  padding: 12px 16px;
  cursor: pointer;
  border-left: 3px solid transparent;
  transition: all 0.2s ease;
  border-radius: 0 8px 8px 0;
  margin-bottom: 8px;
}

.productItem:hover {
  background-color: #f0f2ff;
  border-left-color: #1890ff;
  transform: translateX(2px);
}

.productHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.productTitle {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.productPreview {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 6px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.4;
}

.productMeta {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 输入框样式 */
.inputContainer {
  padding: 16px 24px;
  border-top: 1px solid #f0f0f0;
  background: #ffffff;
}

.inputWrapper {
  display: flex;
  gap: 12px;
  align-items: flex-end;
}

.textArea {
  flex: 1;
  border-radius: 8px;
  resize: none;
  font-size: 14px;
}

.sendButton {
  border-radius: 8px;
  height: 40px;
  padding: 0 20px;
}

/* 标签样式 */
.typeTag {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
}

.selectedTag {
  background: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .rightPanel {
    width: 350px;
  }
}

@media (max-width: 992px) {
  .chatMode {
    flex-direction: column;
  }

  .rightPanel {
    width: 100%;
    height: 40vh;
    border-right: none;
    border-top: 1px solid #f0f0f0;
  }

  .leftPanel {
    border-right: none;
  }
}

@media (max-width: 768px) {
  .welcomeContainer {
    padding: 20px;
  }

  .promptGrid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .messagesContainer {
    padding: 16px;
  }

  .thinkingContainer {
    padding: 16px;
  }

  .sidebarPanel {
    width: 300px;
    left: -300px;
  }
}

@media (max-width: 576px) {
  .panelHeader {
    padding: 12px 16px;
  }

  .inputContainer {
    padding: 12px 16px;
  }

  .messageCard {
    margin-bottom: 12px;
  }

  .inputCard {
    margin: 0 16px;
  }

  .sidebarPanel {
    width: 280px;
    left: -280px;
  }

  .productTitle {
    max-width: 140px;
  }
}

/* 动画效果 */
.fadeIn {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.slideIn {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from { transform: translateX(-20px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

/* 加载状态 */
.typingIndicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px;
  color: #666;
}

.typingDots {
  display: flex;
  gap: 4px;
}

.typingDot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #1890ff;
  animation: typingAnimation 1.4s infinite;
}

.typingDot:nth-child(2) {
  animation-delay: 0.2s;
}

.typingDot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typingAnimation {
  0%, 60%, 100% { transform: translateY(0); opacity: 0.5; }
  30% { transform: translateY(-10px); opacity: 1; }
}

/* 滚动条样式 */
.messagesContainer::-webkit-scrollbar,
.thinkingContainer::-webkit-scrollbar {
  width: 6px;
}

.messagesContainer::-webkit-scrollbar-track,
.thinkingContainer::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.messagesContainer::-webkit-scrollbar-thumb,
.thinkingContainer::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.messagesContainer::-webkit-scrollbar-thumb:hover,
.thinkingContainer::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 聊天消息列表滚动样式 */
.chat-messages-container {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 0;
  max-height: calc(100vh - 180px);
}

.chat-messages-container::-webkit-scrollbar {
  width: 8px;
}

.chat-messages-container::-webkit-scrollbar-track {
  background: transparent;
}

.chat-messages-container::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 4px;
}

.chat-messages-container::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}
