import React, { useState, useRef, useEffect } from 'react';
import { Sender, Bubble } from '@ant-design/x';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import {
  Button,
  Spin,
  App,
  Switch,
  Tooltip,
  Collapse,
  Tag,
  Space,
  Modal
} from 'antd';
import {
  CompressOutlined,
  HistoryOutlined,
  RobotOutlined,
  UserOutlined,
  FileTextOutlined,
  ThunderboltOutlined,
  PlusOutlined,
  MenuOutlined,
  MessageOutlined,
  BulbOutlined,
  ToolOutlined,
  SearchOutlined,
  CodeOutlined,
  ZoomInOutlined,
  CloseOutlined,
  DownloadOutlined
} from '@ant-design/icons';
import { useModel, history } from 'umi';
import { useChat } from './hooks/useChat';
import ConnectionStatus from './components/ConnectionStatus';
import WebSocketTest from './components/WebSocketTest';
import './ChatInterface.module.css';

const ChatInterface: React.FC = () => {
  const { message: messageApi } = App.useApp();

  // 获取用户状态信息
  const { initialState } = useModel('@@initialState');
  const { customData } = initialState || {};

  // 获取当前登录用户的ID
  const currentUserId = customData?.uid;
  const currentUserName = customData?.username;

  const {
    sessions,
    messages,
    loading,
    createNewSession,
    loadUserSessions,
    loadSession,
    sendMessage,
    // 🔥 WebSocket状态和方法
    useWebSocket,
    setUseWebSocket,
    connectionState,
    connectionLatency,
    reconnectCount,
    isWebSocketConnected,
    connectWebSocket,
    disconnectWebSocket,
    getPerformanceMetrics,
    isChainlitCompliant,
    // 🔥 状态重置方法
    setCurrentSession,
    setMessages,
  } = useChat(messageApi, customData);

  const [showLeftPanel, setShowLeftPanel] = useState(true); // 默认显示侧边栏
  const [chatStarted, setChatStarted] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [windowWidth, setWindowWidth] = useState(typeof window !== 'undefined' ? window.innerWidth : 1200);
  const [showWebSocketTest, setShowWebSocketTest] = useState(false); // 🔥 开发模式：WebSocket测试面板
  const [sessionLoading, setSessionLoading] = useState(false); // 独立的会话加载状态
  const [isImageModalVisible, setIsImageModalVisible] = useState(false); // 图片查看器状态
  const [currentImageUrl, setCurrentImageUrl] = useState(''); // 当前查看的图片URL
  const [imageScale, setImageScale] = useState(1); // 图片缩放比例
  const [imagePosition, setImagePosition] = useState({ x: 0, y: 0 }); // 图片位置

  const containerRef = useRef<HTMLDivElement>(null);

  // 🔥 思考过程组件
  const ThinkingProcess: React.FC<{
    thinking: Array<{
      id: string;
      title: string;
      content: string;
      type: 'thinking' | 'toolRequest' | 'toolResult' | 'search' | 'code';
    }>;
    isThinking: boolean;
  }> = ({ thinking, isThinking }) => {
    // 🔥 思考过程始终显示，只要有thinking内容
    if (!thinking || thinking.length === 0) {
      return null;
    }

    const getThinkingIcon = (type: string) => {
      switch (type) {
        case 'thinking':
          return <BulbOutlined style={{ color: '#1890ff' }} />;
        case 'toolRequest':
          return <ToolOutlined style={{ color: '#52c41a' }} />;
        case 'toolResult':
          return <ToolOutlined style={{ color: '#52c41a' }} />;
        case 'search':
          return <SearchOutlined style={{ color: '#fa8c16' }} />;
        case 'code':
          return <CodeOutlined style={{ color: '#722ed1' }} />;
        default:
          return <BulbOutlined style={{ color: '#1890ff' }} />;
      }
    };

    const getThinkingColor = (type: string) => {
      switch (type) {
        case 'thinking':
          return '#1890ff';
        case 'toolRequest':
          return '#52c41a';
        case 'toolResult':
          return '#52c41a';
        case 'search':
          return '#fa8c16';
        case 'code':
          return '#722ed1';
        default:
          return '#1890ff';
      }
    };

    return (
      <div style={{
        marginBottom: isThinking ? '0' : '12px', // 思考中时移除底部间距，让思考过程更突出
        padding: '12px',
        backgroundColor: isThinking ? '#f0f9ff' : '#f8f9fa', // 思考中为浅蓝色，完成后为浅灰色
        borderRadius: '8px',
        border: `1px solid ${isThinking ? '#bae7ff' : '#e9ecef'}`, // 思考中为蓝色边框，完成后为灰色边框
        boxShadow: isThinking ? '0 2px 8px rgba(24, 144, 255, 0.15)' : 'none' // 思考中添加阴影效果
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          marginBottom: '8px',
          fontSize: '14px',
          fontWeight: 600,
          color: isThinking ? '#1890ff' : '#6c757d' // 思考中为蓝色，完成后为灰色
        }}>
          <BulbOutlined style={{ marginRight: '6px', color: isThinking ? '#1890ff' : '#6c757d' }} />
          {isThinking ? '思考过程' : '思考过程（已完成）'}
        </div>
        <Collapse
          defaultActiveKey={isThinking ? thinking.map(item => item.id) : []} // 思考中展开，最终回答时折叠
          ghost
          size="small"
          style={{ backgroundColor: 'transparent' }}
        >
          {thinking.map((item) => (
            <Collapse.Panel
              key={item.id}
              header={
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}>
                  {getThinkingIcon(item.type)}
                  <span style={{ color: getThinkingColor(item.type) }}>
                    {item.title}
                  </span>
                  <Tag color={getThinkingColor(item.type)}>
                    {item.type === 'thinking' ? '思考' :
                      item.type === 'toolRequest' ? '工具请求' :
                        item.type === 'toolResult' ? '工具结果' :
                          item.type === 'search' ? '搜索' : '代码'}
                  </Tag>
                </div>
              }
              style={{
                backgroundColor: 'transparent',
                border: 'none',
                marginBottom: '4px'
              }}
            >
              <div style={{
                padding: '8px 0',
                fontSize: '13px',
                lineHeight: '1.5',
                color: '#495057',
                whiteSpace: 'pre-wrap',
                fontFamily: 'monospace'
              }}>
                {item.content}
              </div>
            </Collapse.Panel>
          ))}
        </Collapse>
      </div>
    );
  };

  // 获取时间问候语
  const getTimeGreeting = () => {
    const hour = new Date().getHours();
    if (hour >= 5 && hour < 12) {
      return '早上好';
    } else if (hour >= 12 && hour < 18) {
      return '下午好';
    } else {
      return '晚上好';
    }
  };

  // 获取用户名
  const getUsername = () => {
    // 从全局状态中获取用户名
    return customData?.username || customData?.name || '用户';
  };

  // 生成个性化问候语
  const getPersonalizedGreeting = () => {
    const timeGreeting = getTimeGreeting();
    const username = getUsername();
    return `${timeGreeting}，${username}`;
  };

  useEffect(() => {
    const handleResize = () => {
      const newWidth = window.innerWidth;
      setWindowWidth(newWidth);
      if (newWidth <= 768) {
        if (showLeftPanel) {
          setShowLeftPanel(false);
        }
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [showLeftPanel]);

  const handleHistoryClick = async (sessionId: string) => {
    try {
      setSessionLoading(true); // 使用独立的加载状态
      await loadSession(sessionId);
      setChatStarted(true);
      // 移动端自动收起侧边栏
      if (windowWidth <= 768) {
        setShowLeftPanel(false);
      }
    } catch (error) {
      messageApi.error('加载历史会话失败');
    } finally {
      setSessionLoading(false);
    }
  };

  useEffect(() => {
    // 等待 customData 准备好后再加载历史对话
    if (customData?.uid) {
      loadUserSessions();
    }
  }, [customData?.uid, loadUserSessions]); // 依赖 customData.uid 和 loadUserSessions

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    const messageContent = inputValue.trim();
    setInputValue('');
    setChatStarted(true);

    try {
      await sendMessage(messageContent);
    } catch (error) {
      messageApi.error('发送消息失败');
    }
  };

  const handlePromptExample = (example: string) => {
    setInputValue(example);
  };

  // 重置到对话首页面
  const handleNewChat = () => {
    // 重置聊天状态到欢迎界面
    setChatStarted(false);
    setInputValue('');

    // 清除当前会话和消息状态，但不立即创建新会话
    // 新会话将在用户发送第一条消息时自动创建
    setCurrentSession(null);
    setMessages([]);

    // 🔥 重新加载历史对话记录列表，确保显示最新数据
    loadUserSessions();
  };

  const showImageModal = (imageUrl: string) => {
    setCurrentImageUrl(imageUrl);
    setIsImageModalVisible(true);
  };

  const handleImageModalClose = () => {
    setIsImageModalVisible(false);
    setCurrentImageUrl('');
    setImageScale(1);
    setImagePosition({ x: 0, y: 0 });
  };

  // 下载图片
  const downloadImage = (imageUrl: string) => {
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = `image_${Date.now()}.jpg`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // 图片缩放和拖拽处理
  const handleImageWheel = (e: React.WheelEvent) => {
    e.preventDefault();
    const delta = e.deltaY > 0 ? 0.9 : 1.1;
    const newScale = Math.max(0.5, Math.min(3, imageScale * delta));
    setImageScale(newScale);
  };

  const handleImageMouseDown = (e: React.MouseEvent) => {
    if (imageScale <= 1) return;

    e.preventDefault();
    const container = e.currentTarget.parentElement?.parentElement;
    if (container) {
      container.classList.add('grabbing');
    }

    const startX = e.clientX - imagePosition.x;
    const startY = e.clientY - imagePosition.y;

    const handleMouseMove = (e: MouseEvent) => {
      e.preventDefault();
      setImagePosition({
        x: e.clientX - startX,
        y: e.clientY - startY
      });
    };

    const handleMouseUp = () => {
      if (container) {
        container.classList.remove('grabbing');
      }
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  const resetImageView = () => {
    setImageScale(1);
    setImagePosition({ x: 0, y: 0 });
  };

  // 键盘快捷键处理
  useEffect(() => {
    if (!isImageModalVisible) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      switch (e.key) {
        case 'Escape':
          handleImageModalClose();
          break;
        case '0':
        case 'r':
          resetImageView();
          break;
        case '=':
        case '+':
          e.preventDefault();
          setImageScale(prev => Math.min(3, prev * 1.2));
          break;
        case '-':
          e.preventDefault();
          setImageScale(prev => Math.max(0.5, prev * 0.8));
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isImageModalVisible]);

  return (
    <div style={{ height: '100%', display: 'flex', width: '100%', backgroundColor: '#ffffff', fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif', overflow: 'hidden' }}>
      <style>
        {`
          /* 🔥 Chainlit风格动画 */
          @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
          }
          @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
          }
          @keyframes pulse {
            0% {
              opacity: 1;
              transform: scale(1);
            }
            50% {
              opacity: 0.8;
              transform: scale(1.01);
            }
            100% {
              opacity: 1;
              transform: scale(1);
            }
          }
          @keyframes chainlit-stream {
            0% {
              border-left-color: #52c41a;
              background-color: #f6ffed;
            }
            50% {
              border-left-color: #73d13d;
              background-color: #f0f9ff;
            }
            100% {
              border-left-color: #52c41a;
              background-color: #f6ffed;
            }
          }
          @keyframes chainlit-glow {
            0% { box-shadow: 0 0 5px rgba(82, 196, 26, 0.3); }
            50% { box-shadow: 0 0 15px rgba(82, 196, 26, 0.6); }
            100% { box-shadow: 0 0 5px rgba(82, 196, 26, 0.3); }
          }
          @keyframes slideIn {
            from { opacity: 0; transform: translateX(10px); }
            to { opacity: 1; transform: translateX(0); }
          }
          * {
            box-sizing: border-box;
          }
          /* Chainlit实时流式指示器 */
          .chainlit-streaming {
            animation: chainlit-stream 1.2s ease-in-out infinite, chainlit-glow 2s ease-in-out infinite;
          }
          /* 🔥 WebSocket高性能模式指示器 */
          .chainlit-websocket-mode {
            animation: chainlit-glow 3s ease-in-out infinite;
            border-left: 3px solid #1890ff;
            background: linear-gradient(90deg, #e6f7ff 0%, #f0f9ff 100%);
          }
          /* 侧边栏滚动条样式 */
          .sidebar-scroll::-webkit-scrollbar {
            width: 6px;
          }
          .sidebar-scroll::-webkit-scrollbar-track {
            background: transparent;
          }
          .sidebar-scroll::-webkit-scrollbar-thumb {
            background: #d1d5db;
            border-radius: 3px;
          }
          .sidebar-scroll::-webkit-scrollbar-thumb:hover {
            background: #9ca3af;
          }
          /* Claude风格的按钮悬停效果 */
          .claude-button-hover {
            transition: all 0.15s ease;
          }
          .claude-button-hover:hover {
            background-color: #f3f4f6 !important;
            transform: translateY(-1px);
          }
          /* 对话项目的动画 */
          .chat-item {
            animation: slideIn 0.2s ease-out;
          }
          /* 迷你侧边栏样式 */
          .mini-sidebar {
            width: 60px;
            background: #f8f9fa;
            border-left: 1px solid #e5e7eb;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 16px 0;
            gap: 12px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          }
          .mini-sidebar-btn {
            width: 44px;
            height: 44px;
            border-radius: 12px;
            background: #ffffff;
            border: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.15s ease;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
          }
          .mini-sidebar-btn:hover {
            background: #f3f4f6;
            border-color: #d1d5db;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          }
          .mini-sidebar-btn:active {
            transform: translateY(0);
          }
          .mini-sidebar-btn.new-chat {
            background: #1f2937;
            border-color: #1f2937;
          }
          .mini-sidebar-btn.new-chat:hover {
            background: #374151;
            border-color: #374151;
          }
          /* 移动端隐藏迷你侧边栏 */
          @media (max-width: 768px) {
            .mini-sidebar {
              display: none;
            }
          }
          /* 图片操作按钮样式 */
          .image-action-btn {
            opacity: 0.8;
            transition: all 0.2s ease;
          }
          .image-action-btn:hover {
            opacity: 1;
            transform: scale(1.1);
            background-color: rgba(0, 0, 0, 0.8) !important;
          }
          /* 图片查看器样式 */
          .image-viewer-container {
            overflow: hidden;
            position: relative;
          }
          .image-viewer-container.grabbing {
            cursor: grabbing !important;
          }
          .image-viewer-container.grabbing img {
            cursor: grabbing !important;
          }
          /* Markdown中的图片样式 */
          .markdown-image {
            transition: all 0.2s ease;
            /* 防止图片加载时尺寸变化 */
            aspect-ratio: auto;
            contain: layout style paint;
          }
          .markdown-image:hover {
            transform: scale(1.02);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
          }
          /* 图片容器样式 */
          .image-container {
            /* 防止内容跳动 */
            contain: layout style paint;
            /* 确保容器尺寸稳定 */
            box-sizing: border-box;
          }
          /* 图片加载状态 */
          .image-loading {
            opacity: 0.7;
            filter: blur(1px);
          }
          .image-loaded {
            opacity: 1;
            filter: none;
          }
          /* 图片查看器帮助提示样式 */
          .image-viewer-help {
            z-index: 1000;
            pointer-events: none;
            user-select: none;
          }
          @media (max-width: 768px) {
            .image-viewer-help {
              font-size: 10px !important;
              padding: 4px 8px !important;
              bottom: 8px !important;
              max-width: calc(100% - 24px) !important;
            }
          }
          @media (max-width: 480px) {
            .image-viewer-help {
              font-size: 9px !important;
              padding: 3px 6px !important;
              bottom: 6px !important;
              max-width: calc(100% - 16px) !important;
            }
          }

        `}
      </style>

      {/* 主内容区域 */}
      <div style={{
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        maxHeight: '100%',
        minHeight: 0,
        overflow: 'hidden'
      }} ref={containerRef}>

        {/* 主聊天区域 */}
        <div style={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          backgroundColor: '#ffffff',
          position: 'relative',
          height: '100%',
          maxHeight: '100%',
          minHeight: 0,
          overflow: 'hidden'
        }}>
          {sessionLoading ? (
            /* 会话加载中 */
            <div style={{
              flex: 1,
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              padding: '40px 20px'
            }}>
              <Spin size="large" />
              <p style={{ color: '#8c8c8c', marginTop: '16px' }}>正在加载会话...</p>
            </div>
          ) : !chatStarted ? (
            /* 欢迎页面 - Claude风格 */
            <div style={{
              flex: 1,
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              padding: '40px 20px',
              maxWidth: '800px',
              margin: '0 auto',
              width: '100%'
            }}>
              {loading ? (
                <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '16px' }}>
                  <Spin size="large" />
                  <p style={{ color: '#8c8c8c' }}>正在初始化...</p>
                </div>
              ) : (
                <>
                  {/* Claude风格欢迎信息 */}
                  <div style={{
                    textAlign: 'center',
                    marginBottom: '40px'
                  }}>
                    <div style={{
                      width: '64px',
                      height: '64px',
                      borderRadius: '16px',
                      backgroundColor: '#1f2937',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      margin: '0 auto 24px auto',
                      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
                    }}>
                      <RobotOutlined style={{ fontSize: '28px', color: '#ffffff' }} />
                    </div>

                    <h1 style={{
                      fontSize: windowWidth > 768 ? '36px' : '32px',
                      fontWeight: 700,
                      color: '#1f2937',
                      margin: '0 0 16px 0',
                      lineHeight: 1.2,
                      background: 'linear-gradient(135deg, #1f2937 0%, #374151 100%)',
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                      backgroundClip: 'text'
                    }}>
                      {getPersonalizedGreeting()}
                    </h1>

                    <p style={{
                      fontSize: '18px',
                      color: '#6b7280',
                      margin: '0 0 32px 0',
                      lineHeight: 1.6,
                      maxWidth: '600px'
                    }}>
                      I&apos;m an AI assistant created by MindShake. I can help you with analysis, writing, coding, math, and many other tasks.
                    </p>
                  </div>

                  {/* Claude风格输入区域 */}
                  <div style={{
                    width: '100%',
                    maxWidth: '700px',
                    margin: '0 auto'
                  }}>
                    <div style={{
                      position: 'relative',
                      border: '2px solid #e5e7eb',
                      borderRadius: '16px',
                      backgroundColor: '#ffffff',
                      transition: 'all 0.2s ease',
                      minHeight: '120px',
                      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)'
                    }}>
                      <Sender
                        placeholder="今天有什么可以帮助你?"
                        value={inputValue}
                        onChange={setInputValue}
                        onSubmit={handleSendMessage}
                        style={{
                          border: 'none',
                          borderRadius: '16px',
                          backgroundColor: 'transparent',
                          minHeight: '120px',
                          fontSize: '16px',
                          lineHeight: '1.5'
                        }}
                      />


                    </div>
                  </div>

                  {/* 建议提示 - Claude风格 */}
                  <div style={{
                    marginTop: '32px',
                    width: '100%',
                    maxWidth: '700px',
                    margin: '32px auto 0 auto'
                  }}>
                    <div style={{
                      display: 'grid',
                      gridTemplateColumns: windowWidth > 768 ? 'repeat(3, 1fr)' : '1fr',
                      gap: '16px'
                    }}>
                      {[
                        'Help me brainstorm ideas for a project',
                        'Explain a complex topic in simple terms',
                        'Write and improve my code'
                      ].map((prompt, index) => (
                        <div
                          key={index}
                          onClick={() => handlePromptExample(prompt)}
                          className="claude-button-hover"
                          style={{
                            padding: '20px',
                            border: '1px solid #e5e7eb',
                            borderRadius: '12px',
                            backgroundColor: '#ffffff',
                            cursor: 'pointer',
                            transition: 'all 0.2s ease',
                            fontSize: '14px',
                            color: '#374151',
                            lineHeight: '1.5',
                            fontWeight: 500,
                            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)',
                            textAlign: 'center'
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.borderColor = '#d1d5db';
                            e.currentTarget.style.backgroundColor = '#f9fafb';
                            e.currentTarget.style.transform = 'translateY(-2px)';
                            e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.borderColor = '#e5e7eb';
                            e.currentTarget.style.backgroundColor = '#ffffff';
                            e.currentTarget.style.transform = 'translateY(0)';
                            e.currentTarget.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.05)';
                          }}
                        >
                          {prompt}
                        </div>
                      ))}
                    </div>
                  </div>
                </>
              )}
            </div>
          ) : (
            /* 聊天消息区域 - Claude风格 */
            <>
              {/* 🔥 开发模式：WebSocket测试面板 */}
              {showWebSocketTest && process.env.NODE_ENV === 'development' && (
                <div style={{ padding: '16px', borderBottom: '1px solid #e5e7eb' }}>
                  <WebSocketTest
                    getPerformanceMetrics={getPerformanceMetrics}
                    isChainlitCompliant={isChainlitCompliant}
                    connectionState={connectionState}
                    connectionLatency={connectionLatency}
                    isWebSocketConnected={isWebSocketConnected}
                  />
                </div>
              )}

              <div style={{
                flex: 1,
                backgroundColor: '#ffffff',
                height: 'calc(100% - 80px)', // 为输入区域预留80px空间
                maxHeight: 'calc(100% - 80px)',
                minHeight: 0,
                display: 'flex',
                flexDirection: 'column',
                overflow: 'hidden'
              }}>
                <Bubble.List
                  autoScroll
                  className="chat-messages-container"
                  style={{
                    flex: 1,
                    padding: '12px 20px',
                    maxWidth: '800px',
                    margin: '0 auto',
                    width: '100%',
                    overflow: 'auto',
                    minHeight: 0,
                    height: '100%',
                    maxHeight: '100%'
                  }}
                  items={messages.flatMap((message) => {
                    console.log(`[CHAINLIT-UI] 🎨 Rendering message ${message.id} | Content: ${message.content?.length || 0} chars | Streaming: ${message.isStreaming}`);

                    // 🔥 Chainlit风格：基于chunk_sub_type和WebSocket模式的动态样式
                    const getChainlitStyle = (chunkSubType?: string, isStreaming?: boolean, isThinking?: boolean) => {
                      const baseStyle = {
                        transition: 'all 0.1s ease-out', // WebSocket模式更快的过渡
                        borderTop: 'none',
                        borderRight: 'none',
                        borderBottom: 'none'
                      };

                      if (isStreaming) {
                        return {
                          ...baseStyle,
                          backgroundColor: useWebSocket ? '#e6f7ff' : '#f6ffed',
                        };
                      }

                      if (!isThinking) {
                        return {
                          ...baseStyle,
                          // borderLeft: '3px solid #1890ff', // Chainlit蓝色用于完成
                          backgroundColor: '#f0f9ff',
                        };
                      }

                      switch (chunkSubType) {
                        case 'completion':
                          return {
                            ...baseStyle,
                            borderLeft: '3px solid #1890ff', // Chainlit蓝色用于完成
                            backgroundColor: '#f0f9ff',
                          };
                        default:
                          return baseStyle;
                      }
                    };

                    // AI头像组件
                    const aiAvatar = (
                      <div style={{
                        width: '28px',
                        height: '28px',
                        borderRadius: '8px',
                        backgroundColor: '#1f2937',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                      }}>
                        <RobotOutlined style={{ color: '#ffffff', fontSize: '14px' }} />
                      </div>
                    );

                    // 用户头像组件
                    const userAvatar = (
                      <div style={{
                        width: '28px',
                        height: '28px',
                        borderRadius: '50%',
                        overflow: 'hidden',
                        border: '2px solid #e5e7eb',
                        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
                      }}>
                        {customData?.avatar ? (
                          <img
                            src={customData.avatar}
                            alt="用户头像"
                            style={{
                              width: '100%',
                              height: '100%',
                              objectFit: 'cover'
                            }}
                            onError={(e) => {
                              // 图片加载失败时隐藏图片，显示用户名首字母
                              const imgElement = e.currentTarget;
                              const container = imgElement.parentElement;
                              if (container) {
                                imgElement.style.display = 'none';
                                const fallbackDiv = container.querySelector('div') as HTMLDivElement;
                                if (fallbackDiv) {
                                  fallbackDiv.style.display = 'flex';
                                }
                              }
                            }}
                          />
                        ) : null}
                        <div style={{
                          width: '100%',
                          height: '100%',
                          backgroundColor: '#1890ff',
                          display: customData?.avatar ? 'none' : 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          fontSize: '14px',
                          fontWeight: 600,
                          color: '#ffffff'
                        }}>
                          {getUsername().substring(0, 1).toUpperCase()}
                        </div>
                      </div>
                    );

                    const bubbleItems = [];

                    if (message.type === 'ai') {
                      // 1. 🔥 思考过程独立成一个对话框
                      if (message.thinking && message.thinking.length > 0) {
                        bubbleItems.push({
                          key: `${message.id}-thinking`,
                          role: 'ai',
                          content: (
                            <ThinkingProcess
                              thinking={message.thinking}
                              isThinking={message.isThinking || false}
                            />
                          ),
                          avatar: aiAvatar,
                          placement: 'start' as const,
                          typing: false,
                          loading: false,
                          styles: {
                            content: {
                              backgroundColor: 'transparent',
                              color: '#1f2937',
                              borderRadius: '16px',
                              padding: '0', // 思考过程组件内部已有padding
                              fontSize: '15px',
                              lineHeight: '1.6',
                              maxWidth: '100%',
                              wordWrap: 'break-word' as any,
                              overflowWrap: 'break-word' as any,
                              whiteSpace: 'pre-wrap'
                            }
                          }
                        });
                      }

                      // 2. 🔥 AI回复内容：每个内容项独立成一个对话框
                      message.content.forEach((contentItem, index) => {
                        // 检查内容是否为空，如果为空则跳过渲染
                        if (!contentItem.content || contentItem.content.trim() === '') {
                          return;
                        }
                        // 根据内容类型渲染不同的组件
                        let contentElement;
                        switch (contentItem.type) {
                          case 'text':
                            contentElement = (
                              <ReactMarkdown
                                remarkPlugins={[remarkGfm]}
                                components={{
                                  p: ({ children }) => (
                                    <p style={{
                                      margin: '0 0 8px 0',
                                      lineHeight: '1.6',
                                      wordWrap: 'break-word' as any,
                                      overflowWrap: 'break-word' as any,
                                      whiteSpace: 'pre-wrap'
                                    }}>
                                      {children}
                                    </p>
                                  ),
                                  img: ({ src, alt }) => (
                                    <div
                                      className="image-container"
                                      style={{
                                        margin: '8px 0',
                                        textAlign: 'center',
                                        position: 'relative',
                                        display: 'inline-block',
                                        cursor: 'pointer',
                                        minHeight: '50px' // 为加载中的图片预留空间
                                      }}
                                    >
                                      <img
                                        src={src}
                                        alt={alt || '图片'}
                                        className="markdown-image image-loading"
                                        style={{
                                          maxWidth: '100%',
                                          maxHeight: '250px',
                                          borderRadius: '6px',
                                          objectFit: 'contain',
                                          display: 'block',
                                          margin: '0 auto',
                                          boxShadow: '0 1px 4px rgba(0, 0, 0, 0.1)',
                                          cursor: 'pointer',
                                          width: 'auto',
                                          height: 'auto',
                                          minWidth: '200px', // 设置最小宽度
                                          minHeight: '100px', // 设置最小高度
                                          opacity: 1,
                                          transition: 'opacity 0.2s ease'
                                        }}
                                        onClick={() => src && showImageModal(src)}
                                        onError={(e) => {
                                          e.currentTarget.style.display = 'none';
                                          // 显示错误信息
                                          const errorDiv = document.createElement('div');
                                          errorDiv.textContent = '图片加载失败';
                                          errorDiv.style.cssText = 'color: #999; font-size: 12px; text-align: center; padding: 8px;';
                                          e.currentTarget.parentNode?.appendChild(errorDiv);
                                        }}
                                        onLoad={(e) => {
                                          // 图片加载成功后的处理，但不改变尺寸
                                          const img = e.currentTarget;
                                          // 移除动态尺寸调整，保持固定尺寸
                                          img.classList.remove('image-loading');
                                          img.classList.add('image-loaded');
                                        }}
                                      />
                                      {/* 操作按钮 */}
                                      <div style={{
                                        position: 'absolute',
                                        top: '6px',
                                        right: '6px',
                                        display: 'flex',
                                        gap: '3px'
                                      }}>
                                        {/* 放大图标 */}
                                        <div
                                          className="image-action-btn"
                                          style={{
                                            width: '24px',
                                            height: '24px',
                                            backgroundColor: 'rgba(0, 0, 0, 0.6)',
                                            borderRadius: '50%',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            color: '#ffffff',
                                            fontSize: '10px',
                                            backdropFilter: 'blur(4px)',
                                            cursor: 'pointer'
                                          }}
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            src && showImageModal(src);
                                          }}
                                        >
                                          <ZoomInOutlined />
                                        </div>
                                        {/* 下载图标 */}
                                        <div
                                          className="image-action-btn"
                                          style={{
                                            width: '24px',
                                            height: '24px',
                                            backgroundColor: 'rgba(0, 0, 0, 0.6)',
                                            borderRadius: '50%',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            color: '#ffffff',
                                            fontSize: '10px',
                                            backdropFilter: 'blur(4px)',
                                            cursor: 'pointer'
                                          }}
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            src && downloadImage(src);
                                          }}
                                        >
                                          <DownloadOutlined />
                                        </div>
                                      </div>
                                    </div>
                                  ),
                                  code: ({ children, className }) => {
                                    const isInline = !className;
                                    return isInline ? (
                                      <code style={{
                                        backgroundColor: '#f3f4f6',
                                        padding: '2px 4px',
                                        borderRadius: '4px',
                                        fontSize: '14px',
                                        fontFamily: 'monospace',
                                        color: '#d97706',
                                        wordBreak: 'break-word',
                                        whiteSpace: 'pre-wrap'
                                      }}>
                                        {children}
                                      </code>
                                    ) : (
                                      <pre style={{
                                        backgroundColor: '#f8f9fa',
                                        padding: '12px',
                                        borderRadius: '6px',
                                        overflow: 'auto',
                                        margin: '8px 0',
                                        border: '1px solid #e9ecef',
                                        maxWidth: '100%',
                                        wordWrap: 'break-word',
                                        whiteSpace: 'pre-wrap'
                                      }}>
                                        <code style={{
                                          fontFamily: 'monospace',
                                          fontSize: '14px',
                                          color: '#374151',
                                          wordBreak: 'break-word',
                                          whiteSpace: 'pre-wrap'
                                        }}>
                                          {children}
                                        </code>
                                      </pre>
                                    );
                                  },
                                  h1: ({ children }) => (
                                    <h1 style={{
                                      fontSize: '24px',
                                      fontWeight: 700,
                                      margin: '16px 0 8px 0',
                                      color: '#1f2937',
                                      lineHeight: '1.3'
                                    }}>
                                      {children}
                                    </h1>
                                  ),
                                  h2: ({ children }) => (
                                    <h2 style={{
                                      fontSize: '20px',
                                      fontWeight: 600,
                                      margin: '14px 0 6px 0',
                                      color: '#1f2937',
                                      lineHeight: '1.3'
                                    }}>
                                      {children}
                                    </h2>
                                  ),
                                  h3: ({ children }) => (
                                    <h3 style={{
                                      fontSize: '18px',
                                      fontWeight: 600,
                                      margin: '12px 0 6px 0',
                                      color: '#1f2937',
                                      lineHeight: '1.3'
                                    }}>
                                      {children}
                                    </h3>
                                  ),
                                  h4: ({ children }) => (
                                    <h4 style={{
                                      fontSize: '16px',
                                      fontWeight: 600,
                                      margin: '10px 0 4px 0',
                                      color: '#1f2937',
                                      lineHeight: '1.3'
                                    }}>
                                      {children}
                                    </h4>
                                  ),
                                  h5: ({ children }) => (
                                    <h5 style={{
                                      fontSize: '14px',
                                      fontWeight: 600,
                                      margin: '8px 0 4px 0',
                                      color: '#1f2937',
                                      lineHeight: '1.3'
                                    }}>
                                      {children}
                                    </h5>
                                  ),
                                  h6: ({ children }) => (
                                    <h6 style={{
                                      fontSize: '12px',
                                      fontWeight: 600,
                                      margin: '6px 0 2px 0',
                                      color: '#1f2937',
                                      lineHeight: '1.3'
                                    }}>
                                      {children}
                                    </h6>
                                  ),
                                  ul: ({ children }) => (
                                    <ul style={{
                                      margin: '8px 0',
                                      paddingLeft: '20px',
                                      lineHeight: '1.6'
                                    }}>
                                      {children}
                                    </ul>
                                  ),
                                  ol: ({ children }) => (
                                    <ol style={{
                                      margin: '8px 0',
                                      paddingLeft: '20px',
                                      lineHeight: '1.6'
                                    }}>
                                      {children}
                                    </ol>
                                  ),
                                  li: ({ children }) => (
                                    <li style={{
                                      margin: '2px 0',
                                      lineHeight: '1.6'
                                    }}>
                                      {children}
                                    </li>
                                  ),
                                  blockquote: ({ children }) => (
                                    <blockquote style={{
                                      margin: '8px 0',
                                      padding: '8px 12px',
                                      borderLeft: '4px solid #e5e7eb',
                                      backgroundColor: '#f9fafb',
                                      borderRadius: '4px',
                                      fontStyle: 'italic',
                                      color: '#6b7280'
                                    }}>
                                      {children}
                                    </blockquote>
                                  ),
                                  table: ({ children }) => (
                                    <div style={{
                                      overflow: 'auto',
                                      margin: '8px 0',
                                      maxWidth: '100%'
                                    }}>
                                      <table style={{
                                        borderCollapse: 'collapse',
                                        width: '100%',
                                        minWidth: '300px'
                                      }}>
                                        {children}
                                      </table>
                                    </div>
                                  ),
                                  th: ({ children }) => (
                                    <th style={{
                                      border: '1px solid #e5e7eb',
                                      padding: '8px 12px',
                                      backgroundColor: '#f9fafb',
                                      fontWeight: 600,
                                      textAlign: 'left',
                                      fontSize: '14px'
                                    }}>
                                      {children}
                                    </th>
                                  ),
                                  td: ({ children }) => (
                                    <td style={{
                                      border: '1px solid #e5e7eb',
                                      padding: '8px 12px',
                                      fontSize: '14px',
                                      lineHeight: '1.4'
                                    }}>
                                      {children}
                                    </td>
                                  ),
                                  a: ({ href, children }) => (
                                    <a
                                      href={href}
                                      target="_blank"
                                      rel="noopener noreferrer"
                                      style={{
                                        color: '#1890ff',
                                        textDecoration: 'none',
                                        borderBottom: '1px solid transparent',
                                        transition: 'border-bottom-color 0.2s ease'
                                      }}
                                      onMouseEnter={(e) => {
                                        e.currentTarget.style.borderBottomColor = '#1890ff';
                                      }}
                                      onMouseLeave={(e) => {
                                        e.currentTarget.style.borderBottomColor = 'transparent';
                                      }}
                                    >
                                      {children}
                                    </a>
                                  ),
                                  hr: () => (
                                    <hr style={{
                                      border: 'none',
                                      borderTop: '1px solid #e5e7eb',
                                      margin: '16px 0',
                                      height: '1px'
                                    }} />
                                  )
                                }}
                              >
                                {contentItem.content}
                              </ReactMarkdown>
                            );
                            break;
                          case 'code':
                            contentElement = (
                              <pre style={{
                                backgroundColor: '#f8f9fa',
                                padding: '12px',
                                borderRadius: '6px',
                                overflow: 'auto',
                                margin: '0',
                                border: '1px solid #e9ecef',
                                maxWidth: '100%',
                                wordWrap: 'break-word',
                                whiteSpace: 'pre-wrap'
                              }}>
                                <code style={{
                                  fontFamily: 'monospace',
                                  fontSize: '14px',
                                  color: '#374151',
                                  wordBreak: 'break-word',
                                  whiteSpace: 'pre-wrap'
                                }}>
                                  {contentItem.content}
                                </code>
                              </pre>
                            );
                            break;
                          case 'image':
                            contentElement = (
                              <div
                                className="image-container"
                                style={{
                                  margin: '0',
                                  textAlign: 'center',
                                  position: 'relative',
                                  display: 'inline-block',
                                  cursor: 'pointer',
                                  minHeight: '80px' // 为加载中的图片预留空间
                                }}
                              >
                                <img
                                  src={contentItem.content}
                                  alt="图片"
                                  className="image-loading"
                                  style={{
                                    maxWidth: '100%',
                                    maxHeight: '400px',
                                    borderRadius: '8px',
                                    objectFit: 'contain',
                                    display: 'block',
                                    margin: '0 auto',
                                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                                    cursor: 'pointer',
                                    width: 'auto',
                                    height: 'auto',
                                    minWidth: '300px', // 设置最小宽度
                                    minHeight: '150px', // 设置最小高度
                                    opacity: 1,
                                    transition: 'opacity 0.2s ease'
                                  }}
                                  onClick={() => contentItem.content && showImageModal(contentItem.content)}
                                  onError={(e) => {
                                    e.currentTarget.style.display = 'none';
                                    // 显示错误信息
                                    const errorDiv = document.createElement('div');
                                    errorDiv.textContent = '图片加载失败';
                                    errorDiv.style.cssText = 'color: #999; font-size: 12px; text-align: center; padding: 8px;';
                                    e.currentTarget.parentNode?.appendChild(errorDiv);
                                  }}
                                  onLoad={(e) => {
                                    // 图片加载成功后的处理，但不改变尺寸
                                    const img = e.currentTarget;
                                    // 移除动态尺寸调整，保持固定尺寸
                                    img.classList.remove('image-loading');
                                    img.classList.add('image-loaded');
                                  }}
                                />
                                {/* 操作按钮 */}
                                <div style={{
                                  position: 'absolute',
                                  top: '8px',
                                  right: '8px',
                                  display: 'flex',
                                  gap: '4px'
                                }}>
                                  {/* 放大图标 */}
                                  <div
                                    className="image-action-btn"
                                    style={{
                                      width: '32px',
                                      height: '32px',
                                      backgroundColor: 'rgba(0, 0, 0, 0.6)',
                                      borderRadius: '50%',
                                      display: 'flex',
                                      alignItems: 'center',
                                      justifyContent: 'center',
                                      color: '#ffffff',
                                      fontSize: '14px',
                                      backdropFilter: 'blur(4px)',
                                      cursor: 'pointer'
                                    }}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      contentItem.content && showImageModal(contentItem.content);
                                    }}
                                  >
                                    <ZoomInOutlined />
                                  </div>
                                  {/* 下载图标 */}
                                  <div
                                    className="image-action-btn"
                                    style={{
                                      width: '32px',
                                      height: '32px',
                                      backgroundColor: 'rgba(0, 0, 0, 0.6)',
                                      borderRadius: '50%',
                                      display: 'flex',
                                      alignItems: 'center',
                                      justifyContent: 'center',
                                      color: '#ffffff',
                                      fontSize: '14px',
                                      backdropFilter: 'blur(4px)',
                                      cursor: 'pointer'
                                    }}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      contentItem.content && downloadImage(contentItem.content);
                                    }}
                                  >
                                    <DownloadOutlined />
                                  </div>
                                </div>
                              </div>
                            );
                            break;
                          case 'link':
                            contentElement = (
                              <a
                                href={contentItem.content}
                                target="_blank"
                                rel="noopener noreferrer"
                                style={{
                                  color: '#1890ff',
                                  textDecoration: 'none',
                                  display: 'block',
                                  margin: '0'
                                }}
                              >
                                {contentItem.content}
                              </a>
                            );
                            break;
                          case 'table':
                            contentElement = (
                              <div style={{
                                overflow: 'auto',
                                margin: '0',
                                maxWidth: '100%'
                              }}>
                                <div
                                  dangerouslySetInnerHTML={{ __html: contentItem.content }}
                                  style={{
                                    minWidth: '300px'
                                  }}
                                />
                              </div>
                            );
                            break;
                          case 'markdown':
                            contentElement = (
                              <ReactMarkdown
                                remarkPlugins={[remarkGfm]}
                                components={{
                                  p: ({ children }) => (
                                    <p style={{
                                      margin: '0 0 8px 0',
                                      lineHeight: '1.6',
                                                                    wordWrap: 'break-word',
                              overflowWrap: 'break-word',
                              whiteSpace: 'pre-wrap'
                                    }}>
                                      {children}
                                    </p>
                                  ),
                                  img: ({ src, alt }) => (
                                    <div style={{
                                      margin: '8px 0',
                                      textAlign: 'center',
                                      position: 'relative',
                                      display: 'inline-block',
                                      cursor: 'pointer'
                                    }}>
                                      <img
                                        src={src}
                                        alt={alt || '图片'}
                                        style={{
                                          maxWidth: '100%',
                                          maxHeight: '400px',
                                          borderRadius: '8px',
                                          objectFit: 'contain',
                                          display: 'block',
                                          margin: '0 auto',
                                          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                                          cursor: 'pointer'
                                        }}
                                        onClick={() => src && showImageModal(src)}
                                        onError={(e) => {
                                          e.currentTarget.style.display = 'none';
                                          // 显示错误信息
                                          const errorDiv = document.createElement('div');
                                          errorDiv.textContent = '图片加载失败';
                                          errorDiv.style.cssText = 'color: #999; font-size: 12px; text-align: center; padding: 8px;';
                                          e.currentTarget.parentNode?.appendChild(errorDiv);
                                        }}
                                        onLoad={(e) => {
                                          // 图片加载成功后的处理
                                          const img = e.currentTarget;
                                          const parent = img.parentElement;
                                          if (parent) {
                                            // 根据图片实际尺寸调整容器
                                            const aspectRatio = img.naturalWidth / img.naturalHeight;
                                            if (aspectRatio > 2) {
                                              // 宽图，限制最大宽度
                                              img.style.maxWidth = '600px';
                                            } else if (aspectRatio < 0.5) {
                                              // 长图，限制最大高度
                                              img.style.maxHeight = '500px';
                                            }
                                          }
                                        }}
                                      />
                                      {/* 操作按钮 */}
                                      <div style={{
                                        position: 'absolute',
                                        top: '8px',
                                        right: '8px',
                                        display: 'flex',
                                        gap: '4px'
                                      }}>
                                        {/* 放大图标 */}
                                        <div
                                          className="image-action-btn"
                                          style={{
                                            width: '32px',
                                            height: '32px',
                                            backgroundColor: 'rgba(0, 0, 0, 0.6)',
                                            borderRadius: '50%',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            color: '#ffffff',
                                            fontSize: '14px',
                                            backdropFilter: 'blur(4px)',
                                            cursor: 'pointer'
                                          }}
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            src && showImageModal(src);
                                          }}
                                        >
                                          <ZoomInOutlined />
                                        </div>
                                        {/* 下载图标 */}
                                        <div
                                          className="image-action-btn"
                                          style={{
                                            width: '32px',
                                            height: '32px',
                                            backgroundColor: 'rgba(0, 0, 0, 0.6)',
                                            borderRadius: '50%',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            color: '#ffffff',
                                            fontSize: '14px',
                                            backdropFilter: 'blur(4px)',
                                            cursor: 'pointer'
                                          }}
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            src && downloadImage(src);
                                          }}
                                        >
                                          <DownloadOutlined />
                                        </div>
                                      </div>
                                    </div>
                                  ),
                                  code: ({ children, className }) => {
                                    const isInline = !className;
                                    return isInline ? (
                                      <code style={{
                                        backgroundColor: '#f3f4f6',
                                        padding: '2px 4px',
                                        borderRadius: '4px',
                                        fontSize: '14px',
                                        fontFamily: 'monospace',
                                        color: '#d97706',
                                        wordBreak: 'break-word',
                                        whiteSpace: 'pre-wrap'
                                      }}>
                                        {children}
                                      </code>
                                    ) : (
                                      <pre style={{
                                        backgroundColor: '#f8f9fa',
                                        padding: '12px',
                                        borderRadius: '6px',
                                        overflow: 'auto',
                                        margin: '8px 0',
                                        border: '1px solid #e9ecef',
                                        maxWidth: '100%',
                                        wordWrap: 'break-word',
                                        whiteSpace: 'pre-wrap'
                                      }}>
                                        <code style={{
                                          fontFamily: 'monospace',
                                          fontSize: '14px',
                                          color: '#374151',
                                          wordBreak: 'break-word',
                                          whiteSpace: 'pre-wrap'
                                        }}>
                                          {children}
                                        </code>
                                      </pre>
                                    );
                                  }
                                }}
                              >
                                {contentItem.content}
                              </ReactMarkdown>
                            );
                            break;
                          default:
                            contentElement = (
                              <div style={{
                                margin: '0'
                              }}>
                                {contentItem.content}
                              </div>
                            );
                            break;
                        }

                        // 为每个内容项创建独立的对话框
                        bubbleItems.push({
                          key: `${message.id}-content-${contentItem.id || index}`,
                          role: 'ai',
                          content: contentElement,
                          avatar: aiAvatar,
                          placement: 'start' as const,
                          typing: false,
                          loading: message.isStreaming && !message.content,
                          styles: {
                            content: {
                              backgroundColor: 'transparent',
                              color: '#1f2937',
                              borderRadius: '16px',
                              padding: '12px 16px',
                              fontSize: '15px',
                              lineHeight: '1.6',
                              maxWidth: '100%',
                              wordWrap: 'break-word' as any,
                              overflowWrap: 'break-word' as any,
                              whiteSpace: 'pre-wrap',
                              ...getChainlitStyle(message.chunkSubType, message.isStreaming, message.isThinking)
                            }
                          }
                        });
                      });
                    } else {
                      // 3. 🔥 用户消息保持不变
                      bubbleItems.push({
                        key: message.id,
                        role: message.type,
                        content: message.content && message.content.length > 0 ? (
                          <div>
                            {message.content.map((contentItem) => {
                              switch (contentItem.type) {
                                case 'text':
                                  return (
                                    <div key={contentItem.id}>
                                      {contentItem.content}
                                    </div>
                                  );
                                case 'image':
                                  return (
                                    <img
                                      key={contentItem.id}
                                      src={contentItem.content}
                                      alt="图片"
                                      style={{
                                        maxWidth: '100%',
                                        borderRadius: '8px',
                                        margin: '4px 0'
                                      }}
                                    />
                                  );
                                case 'link':
                                  return (
                                    <a
                                      key={contentItem.id}
                                      href={contentItem.content}
                                      target="_blank"
                                      rel="noopener noreferrer"
                                      style={{
                                        color: '#ffffff',
                                        textDecoration: 'underline'
                                      }}
                                    >
                                      {contentItem.content}
                                    </a>
                                  );
                                default:
                                  return (
                                    <div key={contentItem.id}>
                                      {contentItem.content}
                                    </div>
                                  );
                              }
                            })}
                          </div>
                        ) : null,
                        avatar: userAvatar,
                        placement: 'end' as const,
                        typing: false,
                        loading: false
                      });
                    }

                    return bubbleItems;
                  })}
                  roles={{
                    user: {
                      placement: 'end' as const,
                      variant: 'filled',
                      styles: {
                        content: {
                          backgroundColor: '#1890ff',
                          color: '#ffffff',
                          borderRadius: '18px',
                          padding: '10px 14px',
                          fontSize: '15px',
                          lineHeight: '1.5',
                          maxWidth: '70%',
                          marginLeft: '8px'
                        }
                      }
                    },
                    ai: {
                      placement: 'start' as const,
                      variant: 'borderless',
                      styles: {
                        content: {
                          backgroundColor: 'transparent',
                          color: '#1f2937',
                          borderRadius: '16px',
                          padding: '12px 0',
                          fontSize: '15px',
                          lineHeight: '1.6',
                          maxWidth: '100%'
                        }
                      }
                    }
                  }}
                />
              </div>

              {/* 底部输入区域 - Claude风格 */}
              <div style={{
                borderTop: '1px solid #e5e7eb',
                padding: '12px 20px',
                backgroundColor: '#ffffff',
                position: 'relative',
                bottom: 0,
                zIndex: 10,
                height: '80px',
                minHeight: '80px',
                maxHeight: '80px',
                flexShrink: 0
              }}>
                <div style={{
                  maxWidth: '800px',
                  margin: '0 auto',
                  width: '100%'
                }}>
                  <div style={{
                    position: 'relative',
                    border: '1px solid #d1d5db',
                    borderRadius: '12px',
                    backgroundColor: '#ffffff',
                    transition: 'border-color 0.2s ease'
                  }}>
                    <Sender
                      placeholder="发消息"
                      value={inputValue}
                      onChange={setInputValue}
                      onSubmit={handleSendMessage}
                      style={{
                        border: 'none',
                        borderRadius: '12px',
                        backgroundColor: 'transparent',
                        minHeight: '50px',
                        fontSize: '15px',
                        lineHeight: '1.5'
                      }}
                    />


                  </div>
                </div>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Claude风格右侧边栏 */}
      <div style={{
        width: showLeftPanel ? (windowWidth > 768 ? '320px' : '280px') : (windowWidth > 768 ? '60px' : '0px'),
        backgroundColor: '#f8f9fa',
        borderLeft: '1px solid #e5e7eb',
        display: 'flex',
        flexDirection: 'column',
        transition: 'width 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        overflow: 'hidden',
        position: 'relative'
      }}>
        {showLeftPanel ? (
          <>
            {/* 侧边栏头部 */}
            <div style={{
              padding: '20px 20px 16px 20px',
              borderBottom: '1px solid #e5e7eb',
              backgroundColor: '#ffffff'
            }}>
              {/* Logo和标题 */}
              <div style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                marginBottom: '16px'
              }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}>
                  <div style={{
                    width: '32px',
                    height: '32px',
                    borderRadius: '8px',
                    backgroundColor: '#1f2937',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <RobotOutlined style={{ color: '#ffffff', fontSize: '16px' }} />
                  </div>
                  <h2 style={{
                    margin: 0,
                    fontSize: '18px',
                    fontWeight: 600,
                    color: '#1f2937'
                  }}>
                    AI小壹
                  </h2>
                </div>

                <Button
                  type="text"
                  icon={<CompressOutlined />}
                  size="small"
                  onClick={() => setShowLeftPanel(false)}
                  className="claude-button-hover"
                  style={{
                    color: '#6b7280',
                    border: 'none',
                    boxShadow: 'none',
                    borderRadius: '6px'
                  }}
                />
              </div>

              {/* 新建对话按钮 */}
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleNewChat}
                style={{
                  width: '100%',
                  height: '40px',
                  borderRadius: '8px',
                  backgroundColor: '#1f2937',
                  borderColor: '#1f2937',
                  fontSize: '14px',
                  fontWeight: 500,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '8px',
                  transition: 'all 0.15s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = '#374151';
                  e.currentTarget.style.transform = 'translateY(-1px)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = '#1f2937';
                  e.currentTarget.style.transform = 'translateY(0)';
                }}
              >
                新对话
              </Button>
            </div>

            {/* 对话历史区域 */}
            <div style={{
              flex: 1,
              overflow: 'hidden',
              display: 'flex',
              flexDirection: 'column'
            }}>
              {/* 历史标题 */}
              <div style={{
                padding: '16px 20px 8px 20px',
                fontSize: '12px',
                fontWeight: 600,
                color: '#6b7280',
                textTransform: 'uppercase',
                letterSpacing: '0.05em'
              }}>
                历史对话
              </div>

              {/* 对话列表 */}
              <div
                className="sidebar-scroll"
                style={{
                  flex: 1,
                  overflow: 'auto',
                  padding: '0 12px 20px 12px'
                }}
              >
                {loading && !sessions.length ? (
                  <div style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    padding: '40px 20px',
                    flexDirection: 'column',
                    gap: '12px'
                  }}>
                    <Spin size="small" />
                    <span style={{ fontSize: '13px', color: '#6b7280' }}>Loading conversations...</span>
                  </div>
                ) : sessions.length > 0 ? (
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '2px' }}>
                    {sessions.map((session, index) => (
                      <div
                        key={session.session_id}
                        className="chat-item claude-button-hover"
                        style={{
                          padding: '12px 16px',
                          borderRadius: '8px',
                          cursor: 'pointer',
                          transition: 'all 0.15s ease',
                          backgroundColor: 'transparent',
                          border: '1px solid transparent',
                          animationDelay: `${index * 0.05}s`
                        }}
                        onClick={() => handleHistoryClick(session.session_id)}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.backgroundColor = '#ffffff';
                          e.currentTarget.style.borderColor = '#e5e7eb';
                          e.currentTarget.style.transform = 'translateY(-1px)';
                          e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.08)';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.backgroundColor = 'transparent';
                          e.currentTarget.style.borderColor = 'transparent';
                          e.currentTarget.style.transform = 'translateY(0)';
                          e.currentTarget.style.boxShadow = 'none';
                        }}
                      >
                        <div style={{
                          display: 'flex',
                          alignItems: 'flex-start',
                          gap: '10px'
                        }}>
                          <MessageOutlined style={{
                            color: '#9ca3af',
                            fontSize: '14px',
                            marginTop: '2px',
                            flexShrink: 0
                          }} />
                          <div style={{ flex: 1, minWidth: 0 }}>
                            <div style={{
                              fontSize: '14px',
                              fontWeight: 500,
                              color: '#1f2937',
                              marginBottom: '4px',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap',
                              lineHeight: 1.3
                            }}>
                              {session.title}
                            </div>
                            <div style={{
                              fontSize: '12px',
                              color: '#6b7280',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap',
                              lineHeight: 1.3
                            }}>
                              {session.preview}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div style={{
                    textAlign: 'center',
                    padding: '40px 20px',
                    color: '#9ca3af',
                    fontSize: '13px',
                    lineHeight: 1.5
                  }}>
                    <MessageOutlined style={{
                      fontSize: '32px',
                      color: '#d1d5db',
                      marginBottom: '12px',
                      display: 'block'
                    }} />
                    暂无历史对话记录
                  </div>
                )}
              </div>
            </div>
          </>
        ) : windowWidth > 768 ? (
          /* 迷你侧边栏 - 仅在桌面端显示 */
          <div className="mini-sidebar">
            {/* 展开侧边栏按钮 */}
            <Tooltip title="展开侧边栏" placement="left">
              <div
                className="mini-sidebar-btn"
                onClick={() => setShowLeftPanel(true)}
              >
                <MenuOutlined style={{
                  color: '#374151',
                  fontSize: '18px'
                }} />
              </div>
            </Tooltip>

            {/* 新建对话按钮 */}
            <Tooltip title="新建对话" placement="left">
              <div
                className="mini-sidebar-btn new-chat"
                onClick={handleNewChat}
              >
                <PlusOutlined style={{
                  color: '#ffffff',
                  fontSize: '18px'
                }} />
              </div>
            </Tooltip>

            {/* 对话记录按钮 */}
            <Tooltip title="对话记录" placement="left">
              <div
                className="mini-sidebar-btn"
                onClick={() => setShowLeftPanel(true)}
              >
                <MessageOutlined style={{
                  color: '#374151',
                  fontSize: '18px'
                }} />
              </div>
            </Tooltip>
          </div>
        ) : null}
      </div>

      {/* 图片查看器 */}
      <Modal
        open={isImageModalVisible}
        onCancel={handleImageModalClose}
        footer={null}
        width="70%"
        bodyStyle={{ padding: 0, position: 'relative' }}
        style={{
          top: 20,
          maxWidth: '1200px'
        }}

        closeIcon={
          <div style={{
            position: 'absolute',
            top: '16px',
            right: '16px',
            display: 'flex',
            gap: '8px',
            zIndex: 1001
          }}>
            {/* 重置按钮 */}
            <div
              style={{
                width: '40px',
                height: '40px',
                backgroundColor: 'rgba(0, 0, 0, 0.6)',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: '#ffffff',
                fontSize: '16px',
                backdropFilter: 'blur(4px)',
                transition: 'all 0.2s ease',
                cursor: 'pointer'
              }}
              onClick={resetImageView}
              title="重置视图"
            >
              <CompressOutlined />
            </div>
            {/* 下载按钮 */}
            <div
              style={{
                width: '40px',
                height: '40px',
                backgroundColor: 'rgba(0, 0, 0, 0.6)',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: '#ffffff',
                fontSize: '16px',
                backdropFilter: 'blur(4px)',
                transition: 'all 0.2s ease',
                cursor: 'pointer'
              }}
              onClick={() => currentImageUrl && downloadImage(currentImageUrl)}
              title="下载图片"
            >
              <DownloadOutlined />
            </div>

            {/* 缩放指示器 */}
            {imageScale !== 1 && (
              <div style={{
                position: 'absolute',
                top: '16px',
                left: '16px',
                padding: '8px 12px',
                backgroundColor: 'rgba(0, 0, 0, 0.6)',
                borderRadius: '20px',
                color: '#ffffff',
                fontSize: '14px',
                backdropFilter: 'blur(4px)',
                transition: 'all 0.2s ease',
                zIndex: 1001
              }}>
                {Math.round(imageScale * 100)}%
              </div>
            )}
            {/* 关闭按钮 */}
            <div style={{
              width: '40px',
              height: '40px',
              backgroundColor: 'rgba(0, 0, 0, 0.6)',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: '#ffffff',
              fontSize: '16px',
              backdropFilter: 'blur(4px)',
              transition: 'all 0.2s ease',
              cursor: 'pointer'
            }}>
              <CloseOutlined />
            </div>
          </div>
        }
      >
        <div
          className="image-viewer-container"
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: 'calc(100vh - 120px)',
            backgroundColor: '#000',
            position: 'relative',
            overflow: 'hidden',
            cursor: imageScale > 1 ? 'grab' : 'default'
          }}
          onWheel={handleImageWheel}
        >
          {/* 帮助提示 */}
          <div
            className="image-viewer-help"
            style={{
              position: 'absolute',
              bottom: '12px',
              left: '50%',
              transform: 'translateX(-50%)',
              padding: '6px 12px',
              backgroundColor: 'rgba(0, 0, 0, 0.8)',
              borderRadius: '16px',
              color: '#ffffff',
              fontSize: '11px',
              backdropFilter: 'blur(4px)',
              transition: 'all 0.2s ease',
              opacity: 0.9,
              whiteSpace: 'nowrap',
              maxWidth: 'calc(100% - 48px)',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              zIndex: 1000
            }}
          >
                          滚轮缩放 • 拖拽移动 • ESC关闭 • R重置 • +/-缩放
          </div>
          <div style={{
            position: 'relative',
            transform: `translate(${imagePosition.x}px, ${imagePosition.y}px) scale(${imageScale})`,
            transition: imageScale === 1 ? 'transform 0.3s ease' : 'none',
            cursor: imageScale > 1 ? 'grab' : 'default'
          }}>
            <img
              src={currentImageUrl}
              alt="图片"
              style={{
                maxWidth: '100%',
                maxHeight: '100%',
                objectFit: 'contain',
                borderRadius: '4px',
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',
                userSelect: 'none',
                pointerEvents: imageScale > 1 ? 'auto' : 'none'
              }}
              onMouseDown={handleImageMouseDown}
              onError={(e) => {
                e.currentTarget.style.display = 'none';
                // 显示错误信息
                const errorDiv = document.createElement('div');
                errorDiv.textContent = '图片加载失败';
                errorDiv.style.cssText = 'color: #ffffff; font-size: 16px; text-align: center; padding: 20px; background-color: rgba(0, 0, 0, 0.8); border-radius: 8px;';
                e.currentTarget.parentNode?.appendChild(errorDiv);
              }}
              onLoad={(e) => {
                // 图片加载成功后的处理
                const img = e.currentTarget;
                const parent = img.parentElement;
                if (parent) {
                  // 根据图片实际尺寸调整容器
                  const aspectRatio = img.naturalWidth / img.naturalHeight;
                  if (aspectRatio > 2) {
                    // 宽图，限制最大宽度
                    img.style.maxWidth = '100%';
                    img.style.maxHeight = 'calc(100vh - 160px)';
                  } else if (aspectRatio < 0.5) {
                    // 长图，限制最大高度
                    img.style.maxHeight = 'calc(100vh - 160px)';
                    img.style.maxWidth = 'calc(100vh - 160px) * 2';
                  }
                }
              }}
            />
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default ChatInterface;
