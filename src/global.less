html,
body,
#root {
  height: 100%;
  margin: 0;
  padding: 0;
  max-width: 100vw;
  overflow-x: hidden;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON><PERSON>,
    'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
}

.colorWeak {
  filter: invert(80%);
}

.ant-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.ant-pro-sider.ant-layout-sider.ant-pro-sider-fixed {
  left: unset;
}

// 主内容区域占用剩余空间，限制在视口内
.ant-pro-layout-content {
  flex: 1 0 auto;
  max-height: 100vh; // 限制最大高度不超过视口
  max-width: 100vw; // 防止容器宽度超出视口
  overflow-x: hidden; // 隐藏水平滚动条
  overflow-y: auto; // 允许垂直滚动但限制在容器内
  box-sizing: border-box; // 确保padding和border包含在宽度内
}

// 页脚始终在底部
.ant-pro-global-footer {
  flex-shrink: 0;
}

// 确保布局容器不超出视口宽度
.ant-pro-layout-container {
  max-width: 100vw;
  overflow-x: hidden;
  box-sizing: border-box;
}

// 响应式处理小屏幕 - 确保不超出视口
@media (max-width: 768px) {
  .ant-pro-layout-content {
    max-height: 100vh; // 确保小屏幕也不超出视口
  }
}

@media (max-width: 480px) {
  .ant-pro-layout-content {
    max-height: 100vh; // 确保超小屏幕也不超出视口
  }
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
  list-style: none;
}

@media (max-width: 768px) {
  .ant-table {
    width: 100%;
    overflow-x: auto;
    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;
        > span {
          display: block;
        }
      }
    }
  }
}
