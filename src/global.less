html,
body,
#root {
  height: 100vh;
  max-height: 100vh;
  margin: 0;
  padding: 0;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l,
    'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
}

.colorWeak {
  filter: invert(80%);
}

.ant-layout {
  height: 100vh;
  max-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.ant-pro-sider.ant-layout-sider.ant-pro-sider-fixed {
  left: unset;
}

// 主内容区域占用剩余空间，严格限制在视口内
.ant-pro-layout-content {
  flex: 1;
  height: 100%;
  max-height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

// 页脚始终在底部
.ant-pro-global-footer {
  flex-shrink: 0;
}

// 确保容器始终在视口内，不管屏幕大小
.ant-pro-layout-container {
  height: 100%;
  max-height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

// 针对聊天页面的特殊优化
.ant-pro-layout-content:has([class*="chat-interface-container"]) {
  height: 100vh;
  max-height: 100vh;
  overflow: hidden;
}

// 响应式处理 - 确保在所有屏幕尺寸下都不超出视口
@media (max-width: 1366px) and (max-height: 768px) {
  .ant-pro-layout-content {
    height: 100vh;
    max-height: 100vh;
  }
}

@media (max-width: 768px) {
  .ant-pro-layout-content {
    height: 100vh;
    max-height: 100vh;
  }
}

@media (max-width: 480px) {
  .ant-pro-layout-content {
    height: 100vh;
    max-height: 100vh;
  }
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
  list-style: none;
}

@media (max-width: 768px) {
  .ant-table {
    width: 100%;
    overflow-x: auto;
    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;
        > span {
          display: block;
        }
      }
    }
  }
}
