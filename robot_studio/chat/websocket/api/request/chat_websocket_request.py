"""
WebSocket聊天请求VO类
"""

from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field

from robot_studio.common.base_request import BaseRequest


class MessageContent(BaseModel):
    """消息内容格式"""
    role: str
    content: str
    message_type: str
    cid: Optional[str] = None
    uid: Optional[str] = None
    user_name: Optional[str] = None


class WebSocketMessage(BaseModel):
    """WebSocket消息格式"""
    type: str  # 'chat', 'ping', 'pong', 'error', 'ack'
    data: Optional[Dict[str, Any]] = None
    id: Optional[str] = None
    timestamp: Optional[float] = None


class WebSocketChatRequest(BaseRequest):
    """WebSocket聊天请求格式"""
    session_id: str = Field(..., description="会话ID")
    user_id: str = Field(..., description="用户ID")
    cid: Optional[str] = Field(default=None, description="企业ID")
    uid: Optional[str] = Field(default=None, description="用户唯一标识")
    user_name: Optional[str] = Field(default=None, description="用户名称")
    contents: List[MessageContent] = Field(..., description="消息内容列表")
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="元数据")
    stream: Optional[bool] = Field(default=True, description="是否流式响应")
    ai_message_id: Optional[str] = Field(default=None, description="AI消息ID")

    def get_last_user_message(self) -> Optional[str]:
        """获取最后一条用户消息内容"""
        for content in reversed(self.contents):
            if content.role == "user" and content.content:
                return content.content
        return None


class PingRequest(BaseModel):
    """心跳请求"""
    timestamp: Optional[float] = None


class ChatProcessRequest(BaseModel):
    """聊天处理请求 - 内部服务使用"""
    websocket_request: WebSocketChatRequest
    connection_id: str
    assistant_message_id: Optional[str] = None
    frontend_ai_message_id: Optional[str] = None
