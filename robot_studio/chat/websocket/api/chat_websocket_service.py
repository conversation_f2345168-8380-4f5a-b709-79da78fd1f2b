"""
WebSocket聊天服务层
提供WebSocket聊天的业务逻辑处理
"""
import json

import logging
from datetime import datetime
from typing import AsyncGenerator, Optional, Dict, Any, List, Tuple

from robot_studio.chat.artifacts.api import ArtifactsService
from robot_studio.component.chat_chunk.manager import ChatChunkManager
from robot_studio.component.chat_chunk.model import Chat<PERSON>hunk, RoleEntity
from robot_studio.component.manage.model import ComponentBase
from robot_studio.component.runtime import ComponentExecutor
from robot_studio.component.runtime.model import ComponentTask
from robot_studio.component.session.api import SessionService
from robot_studio.component.session.manager import SessionManager
from robot_studio.component.session.model import ComponentSession
from robot_studio.common.api_handler import api_handler

from .request import WebSocketChatRequest, ChatProcessRequest
from .result import WebSocketChatResult, PongResult, AckResult

logger = logging.getLogger(__name__)


class ChatWebSocketService:
    """WebSocket聊天服务，提供聊天相关的业务逻辑处理"""

    def __init__(self):
        self._session_service = SessionService()
        self._artifacts_service = ArtifactsService()
        self._chat_chunk_manager = ChatChunkManager()
        self._session_manager = SessionManager()
        self._session_executors = {}  # Cache executors per session

    def _get_component_executor(self, session: ComponentSession) -> ComponentExecutor:
        """
        获取或创建会话的ComponentExecutor
        Args:
            session: 会话对象

        Returns:
            ComponentExecutor: 组件执行器
        """
        session_id = session.session_id
        
        # 检查缓存
        if session_id in self._session_executors:
            logger.info(f"使用缓存的ComponentExecutor: session_id={session_id}")
            return self._session_executors[session_id]
        
        # 从会话配置创建新的ComponentExecutor
        try:
            component_config = session.component_config or {}
            
            # 创建ComponentBase
            component = ComponentBase(
                component_id=component_config.get("component_id", "cmp_9dc834"),
                version=component_config.get("version", 1)
            )
            
            # 创建ComponentExecutor
            executor = ComponentExecutor(component=component)
            
            # 缓存executor
            self._session_executors[session_id] = executor
            
            logger.info(f"为会话创建新的ComponentExecutor: session_id={session_id}, component_id={component.component_id}, version={component.version}")
            return executor
            
        except Exception as e:
            logger.error(f"创建ComponentExecutor失败: session_id={session_id}, error={str(e)}")
            # 使用默认配置作为fallback
            default_component = ComponentBase(
                component_id="cmp_9dc834",
                version=1
            )
            executor = ComponentExecutor(component=default_component)
            self._session_executors[session_id] = executor
            logger.warning(f"使用默认ComponentExecutor作为fallback: session_id={session_id}")
            return executor

    @api_handler
    def create_pong_response(self) -> PongResult:
        """创建心跳响应"""
        return PongResult()

    @api_handler
    def create_ack_response(self, message_id: str) -> AckResult:
        """创建确认响应"""
        return AckResult(id=message_id)

    @api_handler
    def validate_chat_session(self, request: WebSocketChatRequest) -> Tuple[bool, Optional[ComponentSession], Optional[str]]:
        """
        验证聊天会话
        Args:
            request: WebSocket聊天请求

        Returns:
            Tuple[bool, Optional[ComponentSession], Optional[str]]: (是否有效, 会话对象, 错误信息)
        """
        try:
            # 验证会话访问权限
            is_valid, session, error_msg = self._session_service.validate_session_access(
                session_id=request.session_id,
                user_id=request.user_id
            )

            if not is_valid:
                return False, None, error_msg

            return True, session, None

        except Exception as e:
            logger.error(f"验证会话失败: {str(e)}")
            return False, None, f"验证会话失败: {str(e)}"

    @api_handler
    def get_message_artifacts(self, message_id: str) -> List[Dict[str, Any]]:
        """
        获取消息产物列表
        Args:
            message_id: 消息ID

        Returns:
            List[Dict[str, Any]]: 产物列表
        """
        try:
            artifact_result = self._artifacts_service.get_artifacts_by_message(message_id)
            if artifact_result.success and artifact_result.data:
                artifacts = artifact_result.data.get('artifacts', [])
                logger.info(f"获取消息产物成功, 消息ID:{message_id}, 产物数量: {len(artifacts)}")
                return artifacts
        except Exception as e:
            logger.error(f"获取消息产物失败, 消息ID: {message_id}, 错误: {str(e)}")

        return []

    @api_handler
    async def update_session_info(self, session_id: str) -> bool:
        """
        更新会话信息 - 更新消息数量等统计信息
        Args:
            session_id: 会话ID

        Returns:
            bool: 是否更新成功
        """
        try:
            logger.info(f"开始更新会话信息, 会话ID: {session_id}")

            # 获取会话中的消息数量
            session_chunks = self._chat_chunk_manager.get_chat_chunks_by_session_id(session_id)
            message_count = len([chunk for chunk in session_chunks if chunk.chunk_type == "Message"])

            # 更新会话的最后消息时间
            session_info = self._session_manager.query_session_by_id(session_id)
            if session_info:
                session_info.last_message_time = datetime.now()
                session_info.message_count = message_count
                self._session_manager.update_session(session_info)

            logger.info(f"会话信息更新成功, 会话ID: {session_id}, 消息数量: {message_count}")
            return True

        except Exception as e:
            logger.error(f"更新会话信息失败, 会话ID: {session_id}, 错误: {str(e)}")
            return False

    @api_handler
    async def process_chat_stream(
        self,
        request: ChatProcessRequest,
        session: ComponentSession,
        start_time: datetime
    ) -> AsyncGenerator[WebSocketChatResult, None]:
        """
        处理聊天流式响应
        Args:
            request: 聊天处理请求
            session: 会话对象
            start_time: 开始时间

        Yields:
            WebSocketChatResult: 流式聊天结果 (BaseResult格式)
        """
        try:
            # 获取用户消息
            last_user_content = request.websocket_request.get_last_user_message()
            if not last_user_content:
                yield WebSocketChatResult.error_result(
                    request.frontend_ai_message_id,
                    "未找到用户消息"
                )
                return

            logger.info(f"开始WebSocket流式响应生成, 会话:{session.session_id}, 用户消息:'{last_user_content}'")

            # 创建用户消息对象用于组件执行
            user_message = ChatChunk(
                gmt_create=datetime.now(),
                session_id=session.session_id,
                role_type="user",
                role_entity=RoleEntity(
                    user_id=request.websocket_request.user_id,
                    user_name=request.websocket_request.user_name,
                ),
                content=last_user_content,
                chunk_type="Message",
            )

            # 获取会话对应的ComponentExecutor
            component_executor = self._get_component_executor(session)

            # 流式生成响应
            full_response = ""
            chunk_count = 0

            async for chunk in component_executor.reply_chunk_stream(user_message):
                if chunk:
                    chunk_count += 1
                    current_time = datetime.now()
                    elapsed_ms = (current_time - start_time).total_seconds() * 1000

                    # 检测返回的chunk类型并相应处理
                    if isinstance(chunk, ComponentTask):
                        # ComponentTask包含预聚合的内容，不需要额外累积
                        logger.info(f"收到ComponentTask, reply_messages数量: {len(chunk.reply_messages) if chunk.reply_messages else 0}")

                        if chunk.reply_messages:
                            # 从reply_messages中提取完整内容
                            for reply_msg in chunk.reply_messages:
                                if hasattr(reply_msg, 'content') and reply_msg.content:
                                    logger.info(f"ComponentTask完整内容长度: {len(full_response)}")
                                    break

                        # ComponentTask通常是最终结果，跳出循环
                        break

                    else:
                        # ChatChunk对象 - 继续流式处理逻辑
                        # 🔥 智能检测chunk类型和子类型
                        detected_chunk_type, detected_chunk_sub_type = self._detect_chunk_type_and_subtype(chunk)

                        # 🔥 智能提取chunk内容
                        chunk_content = self._extract_chunk_content(chunk, detected_chunk_type, detected_chunk_sub_type)
                        full_response += chunk_content  # 对ChatChunk进行内容累积

                        logger.info(f"发送第{chunk_count}个ChatChunk, 耗时:{elapsed_ms:.1f}ms, 内容长度:{len(chunk_content)}, "
                                  f"chunk_type:{detected_chunk_type}, chunk_sub_type:{detected_chunk_sub_type}, 内容:{chunk_content}")

                        # 生成流式结果 (BaseResult格式)
                        chunk_result = WebSocketChatResult.success_chunk_result(
                            message_id=request.frontend_ai_message_id,
                            content=chunk_content,
                            chunk_id=chunk.chunk_id,
                            chunk_type=detected_chunk_type,
                            chunk_sub_type=detected_chunk_sub_type,
                        )

                        yield chunk_result

            # 获取产物列表
            message_artifacts = self.get_message_artifacts(request.assistant_message_id)

            # 更新会话信息
            await self.update_session_info(session.session_id)

            # 生成完成结果
            end_time = datetime.now()
            total_elapsed_ms = (end_time - start_time).total_seconds() * 1000

            logger.info(f"准备发送完成事件, 总耗时:{total_elapsed_ms:.1f}ms, 总chunk数:{chunk_count}, 响应长度:{len(full_response)}")

            # 计算使用信息
            usage_info = {
                "prompt_tokens": len(last_user_content.split()) if last_user_content else 0,
                "completion_tokens": len(full_response.split()),
                "total_tokens": (len(last_user_content.split()) if last_user_content else 0) + len(full_response.split()),
            }

            completion_result = WebSocketChatResult.completion_result(
                message_id=request.frontend_ai_message_id or request.assistant_message_id,
                full_content="",
                artifacts=message_artifacts,
                usage_info=usage_info
            )

            yield completion_result

            logger.info(f"WebSocket流式聊天完成, 会话:{session.session_id}, 总耗时:{total_elapsed_ms:.1f}ms, 总chunk数:{chunk_count}, 响应长度:{len(full_response)}")

        except Exception as e:
            logger.error(e, f"生成WebSocket流式响应失败, 会话:{session.session_id}")
            yield WebSocketChatResult.error_result(
                request.assistant_message_id,
                str(e)
            )

    def _detect_chunk_type_and_subtype(self, chunk) -> Tuple[str, str]:
        """
        检测chunk的类型和子类型

        Args:
            chunk: ChatChunk对象或其他chunk对象

        Returns:
            Tuple[str, str]: (chunk_type, chunk_sub_type)
        """
        original_chunk_type = chunk.chunk_type
        original_chunk_sub_type = chunk.chunk_sub_type
        # 检查is_session_entity状态，如果为False则认为是agent引用的结果
        if hasattr(chunk.role_entity, 'is_session_entity'):
            is_session_entity = chunk.role_entity.is_session_entity
            if not is_session_entity and original_chunk_type == 'Message':
                original_chunk_type = 'Event'
        return original_chunk_type, original_chunk_sub_type

    def _extract_chunk_content(self, chunk, chunk_type: str, chunk_sub_type: str) -> str:
        """
        提取chunk内容，根据chunk类型和子类型使用不同的提取策略

        Args:
            chunk: chunk对象
            chunk_type: chunk类型 ('Event' 或 'Message')
            chunk_sub_type: chunk子类型

        Returns:
            str: 提取的内容字符串
        """
        try:
            logger.debug(f"提取chunk内容: chunk_type={chunk_type}, chunk_sub_type={chunk_sub_type}")

            # 🔥 根据chunk_sub_type使用不同的内容提取策略
            if chunk_sub_type == 'ModelClientStreamingChunkEvent':
                # 模型流式响应 - 简单字符串内容
                return self._extract_simple_string_content(chunk)

            elif chunk_sub_type == 'TextMessage':
                # 文本消息 - 简单字符串内容
                return self._extract_simple_string_content(chunk)

            elif chunk_sub_type == 'OssUrlMessage':
                # OSS URL消息 - 简单字符串内容（URL）
                return self._extract_simple_string_content(chunk)

            elif chunk_sub_type == 'ThoughtEvent':
                # 思考事件 - 简单字符串内容
                return self._extract_simple_string_content(chunk)

            elif chunk_sub_type == 'ToolCallRequestEvent':
                # 工具调用请求 - List[FunctionCall]内容
                return self._extract_tool_call_request_content(chunk)

            elif chunk_sub_type == 'ToolCallExecutionEvent':
                # 工具执行事件 - List[FunctionExecutionResult]内容
                return self._extract_tool_execution_content(chunk)

            elif chunk_sub_type == 'ToolResultMessage':
                # 工具结果消息 - 可能是简单字符串或复杂对象
                return self._extract_tool_result_message_content(chunk)

            elif chunk_sub_type == 'ToolCallSummaryMessage':
                # 工具调用摘要 - 包含content、tool_calls和results
                return self._extract_tool_summary_content(chunk)

            elif chunk_sub_type == 'CodeGenerationEvent':
                # 代码生成事件 - 包含content、code_blocks和retry_attempt
                return self._extract_code_generation_content(chunk)

            elif chunk_sub_type == 'CodeExecutionEvent':
                # 代码执行事件 - 包含result和retry_attempt
                return self._extract_code_execution_content(chunk)

            elif chunk_sub_type == 'MultiModalMessage':
                # 多模态消息 - List[str | Image]内容
                return self._extract_multimodal_content(chunk)

            else:
                # 🔥 未知子类型，使用通用提取策略
                logger.warning(f"未知chunk_sub_type: {chunk_sub_type}, 使用通用提取策略")
                return self._extract_generic_content(chunk)

        except Exception as e:
            logger.error(f"提取chunk内容失败: {str(e)}, 使用fallback策略")
            return self._extract_generic_content(chunk)

    def _extract_simple_string_content(self, chunk) -> str:
        """提取简单字符串内容"""
        if hasattr(chunk, 'content') and isinstance(chunk.content, str):
            return chunk.content
        elif hasattr(chunk, 'content'):
            return str(chunk.content)
        else:
            return str(chunk)

    def _extract_tool_call_request_content(self, chunk) -> str:
        """提取工具调用请求内容 - List[FunctionCall]"""
        try:
            if hasattr(chunk, 'content') and chunk.content:
                # content是List[FunctionCall]，转换为JSON字符串
                if isinstance(chunk.content, list):
                    import json
                    # 将FunctionCall对象转换为字典
                    tool_calls = []
                    for call in chunk.content:
                        if hasattr(call, 'model_dump'):
                            tool_calls.append(call.model_dump())
                        elif hasattr(call, '__dict__'):
                            tool_calls.append(call.__dict__)
                        else:
                            tool_calls.append(str(call))
                    return json.dumps(tool_calls, ensure_ascii=False)
                else:
                    return str(chunk.content)
            else:
                return self._extract_generic_content(chunk)
        except Exception as e:
            logger.warning(f"提取工具调用请求内容失败: {str(e)}")
            return self._extract_generic_content(chunk)

    def _extract_tool_execution_content(self, chunk) -> str:
        """提取工具执行内容 - List[FunctionExecutionResult]"""
        try:
            if hasattr(chunk, 'content') and chunk.content:
                # content是List[FunctionExecutionResult]，转换为JSON字符串
                if isinstance(chunk.content, list):
                    import json
                    results = []
                    for result in chunk.content:
                        if hasattr(result, 'model_dump'):
                            results.append(result.model_dump())
                        elif hasattr(result, '__dict__'):
                            results.append(result.__dict__)
                        else:
                            results.append(str(result))
                    return json.dumps(results, ensure_ascii=False)
                else:
                    return str(chunk.content)
            else:
                return self._extract_generic_content(chunk)
        except Exception as e:
            logger.warning(f"提取工具执行内容失败: {str(e)}")
            return self._extract_generic_content(chunk)

    def _extract_tool_result_message_content(self, chunk) -> str:
        """提取工具结果消息内容"""
        # ToolResultMessage可能有不同的内容格式，优先使用to_text方法
        try:
            if hasattr(chunk, 'to_text') and callable(chunk.to_text):
                return chunk.to_text()
            elif hasattr(chunk, 'content'):
                return str(chunk.content)
            else:
                return str(chunk)
        except Exception as e:
            logger.warning(f"提取工具结果消息内容失败: {str(e)}")
            return self._extract_generic_content(chunk)

    def _extract_tool_summary_content(self, chunk) -> str:
        """提取工具调用摘要内容 - 包含content、tool_calls和results"""
        try:
            # ToolCallSummaryMessage有content字段（字符串）以及tool_calls和results
            if hasattr(chunk, 'content') and isinstance(chunk.content, str):
                return chunk.content
            elif hasattr(chunk, 'to_text') and callable(chunk.to_text):
                return chunk.to_text()
            else:
                # 如果没有content，尝试组合tool_calls和results信息
                import json
                summary_info = {}
                if hasattr(chunk, 'tool_calls'):
                    summary_info['tool_calls'] = str(chunk.tool_calls)
                if hasattr(chunk, 'results'):
                    summary_info['results'] = str(chunk.results)
                return json.dumps(summary_info, ensure_ascii=False) if summary_info else str(chunk)
        except Exception as e:
            logger.warning(f"提取工具摘要内容失败: {str(e)}")
            return self._extract_generic_content(chunk)

    def _extract_code_generation_content(self, chunk) -> str:
        """提取代码生成内容 - 包含content、code_blocks和retry_attempt"""
        try:
            # CodeGenerationEvent有content字段（字符串）以及code_blocks和retry_attempt
            if hasattr(chunk, 'content') and isinstance(chunk.content, str):
                content = chunk.content

                # 可选：添加额外的代码块和重试信息
                extra_info = []
                if hasattr(chunk, 'retry_attempt') and chunk.retry_attempt > 0:
                    extra_info.append(f"[重试第{chunk.retry_attempt}次]")
                if hasattr(chunk, 'code_blocks') and chunk.code_blocks:
                    extra_info.append(f"[包含{len(chunk.code_blocks)}个代码块]")

                if extra_info:
                    return f"{' '.join(extra_info)} {content}"
                else:
                    return content
            elif hasattr(chunk, 'to_text') and callable(chunk.to_text):
                return chunk.to_text()
            else:
                return str(chunk)
        except Exception as e:
            logger.warning(f"提取代码生成内容失败: {str(e)}")
            return self._extract_generic_content(chunk)

    def _extract_code_execution_content(self, chunk) -> str:
        """提取代码执行内容 - 包含result和retry_attempt"""
        try:
            # CodeExecutionEvent有result字段（CodeResult）和retry_attempt
            if hasattr(chunk, 'result') and chunk.result:
                # CodeResult通常有output字段
                if hasattr(chunk.result, 'output'):
                    content = chunk.result.output
                else:
                    content = str(chunk.result)

                # 添加重试信息
                if hasattr(chunk, 'retry_attempt') and chunk.retry_attempt > 0:
                    return f"[重试第{chunk.retry_attempt}次] {content}"
                else:
                    return content
            elif hasattr(chunk, 'to_text') and callable(chunk.to_text):
                return chunk.to_text()
            else:
                return str(chunk)
        except Exception as e:
            logger.warning(f"提取代码执行内容失败: {str(e)}")
            return self._extract_generic_content(chunk)

    def _extract_multimodal_content(self, chunk) -> str:
        """提取多模态内容 - List[str | Image]"""
        try:
            # MultiModalMessage有content字段（List[str | Image]）
            if hasattr(chunk, 'to_text') and callable(chunk.to_text):
                # 使用to_text方法，它会处理图片占位符
                return chunk.to_text()
            elif hasattr(chunk, 'content') and isinstance(chunk.content, list):
                # 手动处理content列表
                text_parts = []
                for item in chunk.content:
                    if isinstance(item, str):
                        text_parts.append(item)
                    else:
                        # 图片或其他对象，使用占位符
                        text_parts.append("[图片]")
                return " ".join(text_parts)
            else:
                return str(chunk.content) if hasattr(chunk, 'content') else str(chunk)
        except Exception as e:
            logger.warning(f"提取多模态内容失败: {str(e)}")
            return self._extract_generic_content(chunk)

    def _extract_generic_content(self, chunk) -> str:
        """通用内容提取策略 - 作为fallback使用"""
        try:
            # 优先级：to_text() > content属性 > 字符串转换
            if hasattr(chunk, 'to_text') and callable(chunk.to_text):
                return chunk.to_text()
            elif hasattr(chunk, 'content'):
                content = chunk.content
                if isinstance(content, str):
                    return content
                elif content is None:
                    return ""
                else:
                    # 尝试JSON序列化复杂对象
                    try:
                        import json
                        if hasattr(content, 'model_dump'):
                            return json.dumps(content.model_dump(), ensure_ascii=False)
                        elif hasattr(content, '__dict__'):
                            return json.dumps(content.__dict__, ensure_ascii=False)
                        else:
                            return str(content)
                    except Exception:
                        return str(content)
            else:
                return str(chunk)
        except Exception as e:
            logger.warning(f"通用内容提取失败: {str(e)}")
            return str(chunk) if chunk is not None else ""
