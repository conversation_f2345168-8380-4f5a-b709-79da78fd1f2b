"""
统一的聊天块模型定义
合并了session和biz层的ChatChunk模型，以session model为基准
包含聊天块的核心模型、命令、查询和结果类
"""

import json
import uuid
from dataclasses import dataclass
from datetime import datetime
from typing import Optional, List, Dict, Any, Literal

from pydantic import BaseModel, Field

from robot_studio.common.status import Status
from robot_studio.database.mysql.table_schema import ChatChunkDO


class RoleEntity(BaseModel):
    """会话实体 - 基于session model，包含biz model的扩展字段"""

    user_id: str | None = Field(default=None, description="The id of the user")
    """用户ID"""

    user_name: str | None = Field(default=None, description="The name of the user")
    """用户名"""

    is_session_entity: bool = Field(default=True, description="session entity")
    """是否当前会话的主体对象"""

    component_id: str | None = Field(default=None, description="The id of the component")
    """组件ID"""

    component_name: str | None = Field(default=None, description="The name of the component")
    """组件名称"""

    version: int | None = Field(default=None, description="The version of the component")
    """组件版本 - 使用session model的int类型"""


class ChatChunk(BaseModel):
    """统一的会话块模型，组成会话的最小粒度 - 基于session model结构"""

    gmt_create: datetime = Field(default_factory=datetime.now, description="The time of creation")
    """会话时间 - 使用biz model的default_factory"""

    session_id: str | None = Field(default=None, description="The id of the session")
    """关联会话ID"""

    role_type: Literal['user', 'assistant'] | None = Field(default=None, description="The type of the role")
    """会话角色类型 - 使用biz model的可选性"""

    role_entity: RoleEntity | None = Field(default=None, description="The entity of the role")
    """会话角色实体"""

    task_id: str | None = Field(default=None, description="The id of the task")
    """组件任务ID"""

    span_id: str | None = Field(default=None, description="The id of the span")
    """会话块归属的逻辑单元ID"""

    parent_span_id: str | None = Field(default='-', description="The id of the parent span")
    """调用当前逻辑单元ID，组织成树 - 使用session model的默认值"""

    chunk_id: str | None = Field(default=str(uuid.uuid4()), description="The id of the chunk")
    """具体会话块ID，span:chunk == 1:N"""

    chunk_type: Literal['Event', 'Message'] | None = Field(default=None, description="The type of the chunk")
    """会话块类型，Event对应智能体组件的思考过程、工具调用等等过程，消息是最终的结果"""

    chunk_sub_type: str | None = Field(default=None, description="The sub type of the chunk")
    """会话块子类型，如user，agent，tool，mcp等等"""

    chunk_status: str | None = Field(default=Status.SUCCESS.value, description="The status of the chunk")
    """会话块状态，默认为成功"""

    content: Any | None = Field(default=None, description="The content of the chunk")
    """会话块内容"""

    runtime_params: dict | None = Field(default=None, description="runtime  params")
    """运行时参数"""

    @classmethod
    def from_do(cls, chat_chunk_do: ChatChunkDO) -> "ChatChunk":
        """从ChatChunkDO对象创建ChatChunk实例"""
        # Parse role_entity from JSON if present
        role_entity = None
        if chat_chunk_do.role_entity:
            try:
                role_entity_data = json.loads(chat_chunk_do.role_entity)
                role_entity = RoleEntity(**role_entity_data)
            except (json.JSONDecodeError, TypeError):
                role_entity = None

        # Parse runtime_params from JSON if present
        runtime_params = None
        if chat_chunk_do.runtime_params:
            try:
                runtime_params = json.loads(chat_chunk_do.runtime_params)
            except (json.JSONDecodeError, TypeError):
                runtime_params = None

        return cls(
            gmt_create=chat_chunk_do.gmt_create,
            session_id=chat_chunk_do.session_id,
            role_type=chat_chunk_do.role_type,
            role_entity=role_entity,
            task_id=chat_chunk_do.task_id,
            span_id=chat_chunk_do.span_id,
            parent_span_id=chat_chunk_do.parent_span_id,
            chunk_id=chat_chunk_do.chunk_id,
            chunk_type=chat_chunk_do.chunk_type,
            chunk_sub_type=chat_chunk_do.chunk_sub_type,
            chunk_status=chat_chunk_do.chunk_status,
            content=chat_chunk_do.content,
            runtime_params=runtime_params
        )

    def to_do(self) -> ChatChunkDO:
        """转换为ChatChunkDO对象"""
        # Convert role_entity to JSON if present
        role_entity_json = None
        if self.role_entity:
            role_entity_json = json.dumps({
                "user_id": self.role_entity.user_id,
                "user_name": self.role_entity.user_name,
                "is_session_entity": self.role_entity.is_session_entity,
                "component_id": self.role_entity.component_id,
                "component_name": self.role_entity.component_name,
                "version": self.role_entity.version
            })

        # Convert runtime_params to JSON if present
        runtime_params_json = None
        if self.runtime_params:
            runtime_params_json = json.dumps(self.runtime_params)

        return ChatChunkDO(
            chunk_id=self.chunk_id,
            session_id=self.session_id,
            task_id=self.task_id,
            span_id=self.span_id,
            parent_span_id=self.parent_span_id,
            role_type=self.role_type,
            role_entity=role_entity_json,
            chunk_type=self.chunk_type,
            chunk_sub_type=self.chunk_sub_type,
            chunk_status=self.chunk_status,
            content=self.content,
            runtime_params=runtime_params_json,
            gmt_create=self.gmt_create
        )



