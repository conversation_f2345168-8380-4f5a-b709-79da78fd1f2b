from datetime import datetime
from enum import Enum

from pydantic import BaseModel, Field

from robot_studio.database.mysql.table_schema import IterationDO


class IterationStatus(Enum):
    """迭代状态枚举"""
    DEVELOPING = "dev"
    """开发态"""

    BETA = "beta"
    """验证态"""

    RELEASE = "release"
    """正式态"""

    @classmethod
    def from_value(cls, value: str) -> "IterationStatus":
        """
        根据 value 值匹配并返回对应的枚举实例

        Args:
            value: 枚举的 value 值

        Returns:
            IterationStatus: 匹配的枚举实例

        Raises:
            ValueError: 当找不到匹配的枚举时抛出异常
        """
        for member in cls:
            if member.value == value:
                return member
        raise ValueError(f"未找到匹配的 IterationStatus: {value}")


class Iteration(BaseModel):
    """组件迭代模型"""

    iterate_id: str | None = None
    """迭代ID，ITE开头"""

    cid: str | None = None
    """关联的企业ID"""

    gmt_create: datetime | None = None
    """迭代创建时间"""

    gmt_modified: datetime | None = None
    """迭代更新时间"""

    name: str | None = None
    """迭代名称"""

    desc: str | None = None
    """迭代描述"""

    tags: list[str] | None = None
    """迭代标签列表"""

    owner: str | None = None
    """迭代创建人"""

    members: list[str] | None = None
    """迭代成员信息"""

    beta_ratio: float | None = Field(default=0, ge=0, le=1)
    """beta比例"""

    release_ip_list: list[str] | None = None
    """发布的机器IP"""

    status: str | None = None
    """状态"""

    code_release: bool = Field(default=False)
    """迭代是否涉及代码发布"""

    image_id: str | None = None
    """迭代代码发布的目标镜像ID"""

    rollback_image_id: str | None = None
    """迭代回滚的目标镜像ID"""

    @classmethod
    def from_do(cls, iteration_do: IterationDO) -> 'Iteration':
        """
        从IterationDO创建ComponentIteration
        Args:
            iteration_do: 数据库迭代对象
            
        Returns:
            Iteration: 组件迭代模型
        """
        # 根据image_id判断code_change
        return cls(
            iterate_id=iteration_do.iterate_id,
            cid=iteration_do.cid,
            gmt_create=iteration_do.gmt_create,
            gmt_modified=iteration_do.gmt_modified,
            name=iteration_do.name,
            desc=iteration_do.desc,
            tags=iteration_do.tags,
            owner=iteration_do.owner,
            members=iteration_do.members,
            release_ip_list=iteration_do.release_ip_list,
            status=iteration_do.status,
            beta_ratio=iteration_do.beta_ratio,
            code_release=iteration_do.code_release,
            image_id=iteration_do.image_id,
            rollback_image_id=iteration_do.rollback_image_id
        )

    def to_do(self) -> IterationDO:
        """
        转换为IterationDO
        Returns:
            IterationDO: 数据库迭代对象
        """
        return IterationDO(
            iterate_id=self.iterate_id,
            cid=self.cid,
            name=self.name,
            desc=self.desc,
            tags=self.tags,
            owner=self.owner,
            members=self.members,
            release_ip_list=self.release_ip_list,
            status=self.status,
            beta_ratio=self.beta_ratio,
            code_release=self.code_release,
            image_id=self.image_id,
            rollback_image_id=self.rollback_image_id
        )
