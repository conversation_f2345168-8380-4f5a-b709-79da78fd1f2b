import uuid
from typing import Async<PERSON>enerator

from autogen_core.models import ChatCompletionClient, UserMessage, CreateResult

from robot_studio.component.chat_chunk.model import Chat<PERSON>hunk, RoleEntity
from robot_studio.component.manage.model import ComponentBase, Component
from robot_studio.component.runtime.model import ComponentTask, RuntimeContext
from robot_studio.database.mysql.repository import ComponentStateRepository
from ._factory import ComponentFactory
from ..chat_chunk.manager import Chat<PERSON>hunkManager
from ..template.base import BaseComponentAgent, ComponentTaskResult
from ..template.message import TextMessage, BaseSessionChunk, ModelClientStreamingChunkEvent


class ComponentExecutor:

    def __init__(self, component: ComponentBase):
        self._component_factory: ComponentFactory = ComponentFactory()
        self._config = self._component_factory.load_component_config(component)
        self._executor = self._component_factory.load_instance(self._config)
        self._state_repository = ComponentStateRepository()
        self._chunk_manager = ChatChunkManager()

    @property
    def config(self) -> Component:
        return self._config

    async def reply_chunk(self, user_message: ChatChunk) -> ComponentTask:
        """
        组件执行入口
        Args:
            user_message: 用户会话消息，入参为chunk_id或者ChatChunk模型

        Returns:
            ComponentTask: 组件的执行任务状态

        """
        if isinstance(self._executor, BaseComponentAgent):
            # 1. 调用ComponentStateRepository加载组件的state
            component_state = self._state_repository.query_state(
                session_id=user_message.session_id,
                component_id=self._config.component_id,
                version=self._config.version
            )

            # 2. 如果state非空，调用executor的load_state方法，加载state
            if component_state and component_state.state:
                await self._executor.load_state(component_state.state)

            result = await self._executor.run_component(
                ctx=RuntimeContext(session_id=user_message.session_id, task_id=user_message.task_id,
                                   parent_span_id=user_message.span_id or '-'),
                task=TextMessage(source=user_message.role_entity.user_id, content=user_message.content)
            )

            if result:
                return ComponentTask(
                    session_id=user_message.session_id,
                    task_id=user_message.task_id,
                    user_message_id=user_message.chunk_id,
                    status=result.status
                )

        return ComponentTask(
            session_id=user_message.session_id,
            task_id=user_message.task_id,
            user_message_id=user_message.chunk_id
        )

    async def reply_chunk_stream(self, user_message: ChatChunk) -> AsyncGenerator[
        ChatChunk | ComponentTask, None]:
        """
        组件执行流式入口
        Args:
            user_message:

        Returns:
            AsyncGenerator[ChatChunk | ComponentTask, None]: 流式输出ChatChunk、ComponentTask

        """
        user_message.chunk_id = str(uuid.uuid4())
        user_message.chunk_sub_type = 'UserMessage'
        if isinstance(self._executor, ChatCompletionClient):
            user_msg = UserMessage(source=user_message.role_entity.user_id, content=user_message.content)
            async for message in self._executor.create_stream(messages=[user_msg]):
                if isinstance(message, str):
                    yield ChatChunk(content=message, role_type='assistant', chunk_type='Event',
                                    chunk_sub_type='ModelClientStreamingChunkEvent')
                if isinstance(message, CreateResult):
                    chat_chunk = ChatChunk(content=message.content, role_type='assistant', chunk_type='Message')
                    yield ComponentTask(reply_messages=[chat_chunk])
                    return

        if isinstance(self._executor, BaseComponentAgent):
            # 1. 调用ComponentStateRepository加载组件的state
            component_state = self._state_repository.query_state(
                session_id=user_message.session_id,
                component_id=self._config.component_id,
                version=self._config.version
            )

            # 2. 如果state非空，调用executor的load_state方法，加载state
            if component_state and component_state.state:
                await self._executor.load_state(component_state.state)

            reply_chunks = []
            self._chunk_manager.create_chat_chunk(user_message)
            async for message in self._executor.run_component_stream(
                    ctx=RuntimeContext(session_id=user_message.session_id, task_id=user_message.task_id,
                                       parent_span_id=user_message.span_id or '-'),
                    task=TextMessage(source=user_message.role_entity.user_id, content=user_message.content)):
                if isinstance(message, BaseSessionChunk):
                    _chunk = ChatChunk(content=message.content, chunk_status=message.status, role_type='assistant',
                                       chunk_type=message.chunk_type,
                                       chunk_sub_type=message.type, chunk_id=message.id,
                                       parent_span_id=message.parent_span_id, span_id=message.span_id,
                                       session_id=message.session_id, task_id=message.task_id,
                                       role_entity=RoleEntity(component_id=message.component_id,
                                                              version=message.version,
                                                              is_session_entity=message.component_id == self._config.component_id), )

                    yield _chunk
                    if isinstance(message, ModelClientStreamingChunkEvent):
                        continue
                    # 非流式chunk加入回复列表，并持久化
                    reply_chunks.append(_chunk)
                    self._chunk_manager.create_chat_chunk(_chunk)

                if isinstance(message, ComponentTaskResult):
                    yield ComponentTask(session_id=user_message.session_id, task_id=user_message.task_id,
                                        user_message_id=user_message.chunk_id, reply_messages=reply_chunks,
                                        status=message.status)
                    return
