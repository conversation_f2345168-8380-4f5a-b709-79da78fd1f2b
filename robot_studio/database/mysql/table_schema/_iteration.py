from datetime import datetime
from typing import Optional, List

from sqlalchemy import Column
from sqlmodel import SQLModel, Field

from ._json_col import JSONText


class IterationDO(SQLModel, table=True):
    """迭代表模型"""
    __tablename__ = 'iteration'
    id: Optional[int] = Field(default=None, primary_key=True, description="主键")
    gmt_create: datetime = Field(nullable=False, default_factory=lambda: datetime.now(),
                                 description="创建时间")
    gmt_modified: datetime = Field(nullable=False, default_factory=lambda: datetime.now(),
                                   description="修改时间")
    iterate_id: str | None = Field(default=None, max_length=64, description="迭代唯一ID")
    cid: str | None = Field(nullable=True, default=None, max_length=64, description="关联的企业ID")
    name: str | None = Field(nullable=True, max_length=128, description="迭代名称")
    desc: str | None = Field(nullable=True, max_length=512, description="迭代描述")
    tags: Optional[List[str]] = Field(sa_column=Column(JSONText(), nullable=True, default=None),
                                      description="迭代标签列表")
    owner: str | None = Field(nullable=True, max_length=64, description="迭代创建人")
    members: Optional[List[str]] = Field(sa_column=Column(JSONText(), nullable=True, default=None),
                                         description="迭代成员UID列表")
    release_ip_list: Optional[List[str]] = Field(sa_column=Column(JSONText(), nullable=True, default=None),
                                                 description="发布机器IP列表")
    status: str | None = Field(nullable=True, max_length=32, description="组件当前版本状态")
    beta_ratio: float = Field(nullable=True, default=0, description="beta比例")
    code_release: bool | None = Field(nullable=True, default=False, description="当前是否涉及代码发布")
    image_id: str | None = Field(nullable=True, default=None, description="迭代的镜像ID")
    rollback_image_id: str | None = Field(nullable=True, default=None, description="发布回滚的目标镜像ID")
    is_del: bool | None = Field(nullable=True, default=False, description="当前迭代是否删除")
