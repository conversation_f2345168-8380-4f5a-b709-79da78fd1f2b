import logging
import os

from sqlmodel import create_engine, Session

logger = logging.getLogger(__name__)


def database_url(db_name):
    db_url_prod = os.getenv('DB_URL_PROD')
    db_url_dev = os.getenv('DB_URL_DEV', 'rm-bp1r8a1y8w872i170oo.mysql.rds.aliyuncs.com')
    env_tag = os.getenv('ENV_TAG')
    db_url = db_url_prod if env_tag=='PROD' or (env_tag=='TEST' and db_name=='mindshake_common') else db_url_dev
    db_port = 3306
    db_user = _read_secret('db_user')
    db_password = _read_secret('db_password')
    # 添加字符集参数支持emoji等特殊字符
    return f"mysql+pymysql://{db_user}:{db_password}@{db_url}:{db_port}/{db_name}?charset=utf8mb4"

# Default engine for mindshake database
engine = create_engine(database_url("mindshake"), echo=False, pool_pre_ping=True)

# Common database engine for mindshake_common database
# mindshake_common存储环境共享的系统配置信息和组件配置信息
common_engine = create_engine(database_url("mindshake_common"), echo=False, pool_pre_ping=True)


def db_handler(func):
    """数据库操作异常处理装饰器"""

    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(f"Database operation failed: {str(e)}")
            # 可以根据需要处理不同类型的异常
            # 例如: SQLAlchemyError, IntegrityError等
            raise

    return wrapper


def get_session():
    with Session(engine) as session:
        try:
            yield session
        finally:
            session.close()


def get_common_session():
    with Session(common_engine) as session:
        try:
            yield session
        finally:
            session.close()

def _read_secret(secret_name):
    """从 Docker secret 文件读取值"""
    secret_path = f"/run/secrets/{secret_name}"
    try:
        with open(secret_path, 'r') as f:
            return f.read().strip()
    except FileNotFoundError:
        # 如果不在 Swarm 环境，尝试从环境变量读取
        return os.environ.get(secret_name.upper())


if __name__ == '__main__':
    conn_session = get_session()
    print(type(conn_session))
