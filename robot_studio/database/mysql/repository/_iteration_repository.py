import logging
from datetime import datetime
from typing import Sequence, Dict, Any

from sqlmodel import Session, select

from robot_studio.database.mysql.db_engine import engine, db_handler
from robot_studio.database.mysql.table_schema import IterationDO
from robot_studio.utils.uuid import build_uuid, DataType

logger = logging.getLogger(__name__)


class IterationRepository:
    """迭代仓库，提供对迭代的CRUD操作"""

    def __init__(self):
        self._engine = engine

    @db_handler
    def create_iteration(self, iteration: IterationDO) -> IterationDO:
        """
        创建迭代
        Args:
            iteration: 迭代信息

        Returns:
            IterationDO: 创建后的迭代信息
        """
        # 生成迭代ID
        if not iteration.iterate_id:
            iteration.iterate_id = build_uuid(DataType.ITERATION)

        # 添加到数据库并提交
        with Session(self._engine) as session:
            session.add(iteration)
            session.commit()
            session.refresh(iteration)
        return iteration

    @db_handler
    def get_iteration_by_id(self, iterate_id: str) -> IterationDO | None:
        """
        根据迭代ID查询迭代信息
        Args:
            iterate_id: 迭代ID

        Returns:
            IterationDO | None: 迭代信息，不存在则返回None
        """
        with Session(self._engine) as session:
            statement = select(IterationDO).where(
                IterationDO.iterate_id == iterate_id,
                IterationDO.is_del == False
            )
            return session.exec(statement).one_or_none()

    @db_handler
    def get_iterations_by_cid(self, cid: str) -> Sequence[IterationDO]:
        """
        根据企业ID查询所有未删除的迭代
        Args:
            cid: 企业ID

        Returns:
            Sequence[IterationDO]: 迭代列表
        """
        with Session(self._engine) as session:
            statement = select(IterationDO).where(
                IterationDO.cid == cid,
                IterationDO.is_del == False
            )
            return session.exec(statement).all()

    @db_handler
    def update_iteration(self, iterate_id: str, update_data: Dict[str, Any]) -> IterationDO | None:
        """
        更新迭代
        Args:
            iterate_id: 迭代ID
            update_data: 更新数据字典

        Returns:
            IterationDO | None: 更新后的迭代信息，失败则返回None
        """
        iteration = self.get_iteration_by_id(iterate_id)
        if not iteration:
            logger.error(f"待更新的迭代不存在，iterate_id={iterate_id}")
            return None

        try:
            # 更新迭代属性
            for key, value in update_data.items():
                if hasattr(iteration, key):
                    setattr(iteration, key, value)

            # 更新修改时间
            iteration.gmt_modified = datetime.now()

            # 保存到数据库
            with Session(self._engine) as session:
                session.add(iteration)
                session.commit()
                session.refresh(iteration)
            return iteration
        except Exception as e:
            logger.error(f"更新迭代失败，iterate_id={iterate_id}: {e}")
            return None

    @db_handler
    def delete_iteration(self, iterate_id: str) -> bool:
        """
        删除迭代（软删除）
        Args:
            iterate_id: 迭代ID

        Returns:
            bool: 删除结果
        """
        try:
            iteration = self.get_iteration_by_id(iterate_id)
            if not iteration:
                logger.warning(f"待删除的迭代不存在，iterate_id={iterate_id}")
                return False

            # 软删除
            update_res = self.update_iteration(iterate_id, {"is_del": True})
            return update_res is not None
        except Exception as e:
            logger.error(f"软删除迭代失败，iterate_id={iterate_id}: {e}")
            return False
