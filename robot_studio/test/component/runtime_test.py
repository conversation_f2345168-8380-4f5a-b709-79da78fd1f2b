"""
ComponentExecutor测试文件
"""

import unittest
from datetime import datetime

from robot_studio.component.manage.model import ComponentBase
from robot_studio.component.runtime import ComponentExecutor
from robot_studio.component.chat_chunk.model import Chat<PERSON>hunk, RoleEntity


class TestComponentExecutor(unittest.IsolatedAsyncioTestCase):
    """ComponentExecutor测试类"""

    def setUp(self):
        """测试前准备"""
        self.component_executor = ComponentExecutor(component=ComponentBase(
            component_id="cmp_1ab6fc",
            version=1
        ))

    async def test_reply_chunk_stream_with_chat_completion_client(self):
        """测试reply_chunk_stream方法 - ChatCompletionClient场景"""

        # 准备测试数据
        role_entity = RoleEntity(
            user_id="test_user_001",
            user_name="潘帅好"
        )

        user_message = ChatChunk(
            gmt_create=datetime.now(),
            session_id="test_session_001",
            role_type="user",
            role_entity=role_entity,
            content="牛再再老师介绍一下？调用工具",
            chunk_type="Message"
        )

        # 执行测试
        async for result in self.component_executor.reply_chunk_stream(user_message):
            print(result)


if __name__ == "__main__":
    unittest.main()
