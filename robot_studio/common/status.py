import enum


class Status(str, enum.Enum):
    """会话块状态"""

    PENDING = "pending"
    """挂起"""

    SUCCESS = "success"
    """成功"""

    FAILED = "failed"
    """失败"""

    RUNNING = "running"
    """执行中"""

    @staticmethod
    def is_ready(status: str) -> bool:
        return status in [Status.SUCCESS.value, Status.FAILED.value]

    @staticmethod
    def not_ready(status: str) -> bool:
        return status not in [Status.SUCCESS.value, Status.FAILED.value]